#!/usr/bin/env python3
"""
Test script for the new chevron-based eBird scraper workflow
"""

import os
import sys
from scraperBot import EBirdScraperBot

def test_chevron_workflow():
    """Test the new chevron navigation workflow"""
    print("🧪 Testing Chevron Navigation Workflow")
    print("=" * 50)
    
    # Test URL - Java Sparrow photos
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo"
    output_dir = "test_chevron_output"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Initialize scraper
        print("🚀 Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=False)
        
        # Test with small number of images
        print("📸 Starting chevron navigation test...")
        result = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=output_dir,
            max_images=5,  # Small test batch
            method='click_and_view',
            timeout_minutes=10,
            max_load_more_clicks=0,  # No Load More clicks
            crop_to_bird=False,
            download_method='screenshot'  # Use screenshot method
        )
        
        print(f"\n✅ Test completed! Processed {result} images")
        
        # Check results
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
            print(f"📁 Files created: {len(files)}")
            for file in files:
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   - {file} ({file_size/1024:.1f} KB)")
        
        return result > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            scraper.wd.quit()
        except:
            pass

def main():
    """Main test function"""
    print("🦅 eBird Chevron Navigation Test")
    print("=" * 50)
    
    success = test_chevron_workflow()
    
    if success:
        print("\n✅ Test PASSED - Chevron navigation workflow is working!")
    else:
        print("\n❌ Test FAILED - Check the error messages above")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
