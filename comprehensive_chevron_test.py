#!/usr/bin/env python3
"""
Comprehensive test untuk chevron navigation dengan berbagai skenario
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def test_chevron_with_different_scenarios():
    """Test chevron navigation dengan berbagai skenario"""
    print("🧪 COMPREHENSIVE CHEVRON NAVIGATION TEST")
    print("=" * 60)
    
    # Test URLs yang berbeda
    test_urls = [
        {
            "name": "Java Sparrow",
            "url": "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo",
            "expected_images": 10
        },
        {
            "name": "White-rumped Shama", 
            "url": "https://ebird.org/media/catalog?taxonCode=whirsh1&mediaType=photo",
            "expected_images": 8
        }
    ]
    
    results = []
    
    for test_case in test_urls:
        print(f"\n{'='*40}")
        print(f"🦅 TESTING: {test_case['name']}")
        print(f"🔗 URL: {test_case['url']}")
        print(f"🎯 Expected images: {test_case['expected_images']}")
        print(f"{'='*40}")
        
        output_dir = f"test_{test_case['name'].lower().replace(' ', '_').replace('-', '_')}"
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            # Initialize scraper
            scraper = EBirdScraperBot(headless=False)
            
            # Test dengan jumlah gambar yang diminta
            result = scraper.scrape_ebird(
                ebird_url=test_case['url'],
                output_dir=output_dir,
                max_images=test_case['expected_images'],
                method='click_and_view',
                timeout_minutes=15,
                max_load_more_clicks=0,  # No Load More
                crop_to_bird=False,
                download_method='screenshot'
            )
            
            # Check hasil
            files = []
            if os.path.exists(output_dir):
                files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
            
            test_result = {
                "name": test_case['name'],
                "url": test_case['url'],
                "expected": test_case['expected_images'],
                "actual": len(files),
                "success": result > 0 and len(files) > 1,  # Success jika > 1 gambar
                "files": files
            }
            
            results.append(test_result)
            
            print(f"\n📊 HASIL TEST {test_case['name']}:")
            print(f"   Expected: {test_case['expected_images']} images")
            print(f"   Actual: {len(files)} images")
            print(f"   Status: {'✅ SUCCESS' if test_result['success'] else '❌ FAILED'}")
            
            if files:
                print(f"   Files created:")
                for file in files[:5]:  # Show first 5 files
                    file_path = os.path.join(output_dir, file)
                    file_size = os.path.getsize(file_path)
                    print(f"     - {file} ({file_size/1024:.1f} KB)")
                if len(files) > 5:
                    print(f"     ... and {len(files)-5} more files")
            
        except Exception as e:
            print(f"❌ Test failed for {test_case['name']}: {e}")
            test_result = {
                "name": test_case['name'],
                "url": test_case['url'],
                "expected": test_case['expected_images'],
                "actual": 0,
                "success": False,
                "error": str(e)
            }
            results.append(test_result)
        
        finally:
            try:
                scraper.wd.quit()
            except:
                pass
        
        # Brief pause between tests
        time.sleep(3)
    
    return results

def test_edge_cases():
    """Test edge cases yang mungkin menyebabkan masalah"""
    print(f"\n{'='*40}")
    print("🔍 TESTING EDGE CASES")
    print(f"{'='*40}")
    
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo"
    output_dir = "test_edge_cases"
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        scraper = EBirdScraperBot(headless=False)
        
        # Navigate to URL
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Test 1: Manual step-by-step navigation
        print("\n🧪 Test 1: Manual step-by-step navigation")
        
        # Get first image
        clickable_images = scraper._get_clickable_images_from_page()
        if not clickable_images:
            print("❌ No clickable images found")
            return False
        
        # Enter photo viewer
        if not scraper._click_image_to_enter_viewer(clickable_images[0]):
            print("❌ Failed to enter photo viewer")
            return False
        
        print("✅ Entered photo viewer successfully")
        
        # Test multiple chevron clicks with detailed monitoring
        for i in range(5):
            print(f"\n--- Step {i+1} ---")
            
            # Check current state
            current_url = scraper.wd.current_url
            in_viewer = scraper._is_in_photo_viewer()
            
            print(f"URL: {current_url}")
            print(f"In viewer: {in_viewer}")
            
            if not in_viewer:
                print("❌ Lost photo viewer state!")
                break
            
            # Take screenshot
            screenshot_path = scraper._take_photo_viewer_screenshot(i, output_dir)
            if screenshot_path:
                print(f"✅ Screenshot: {os.path.basename(screenshot_path)}")
            else:
                print("❌ Screenshot failed")
            
            # Check state after screenshot
            current_url_after = scraper.wd.current_url
            in_viewer_after = scraper._is_in_photo_viewer()
            
            if current_url != current_url_after:
                print(f"⚠️ URL changed after screenshot!")
                print(f"   Before: {current_url}")
                print(f"   After: {current_url_after}")
            
            if in_viewer != in_viewer_after:
                print(f"⚠️ Viewer state changed after screenshot!")
                print(f"   Before: {in_viewer}")
                print(f"   After: {in_viewer_after}")
            
            # Try to navigate to next (except last iteration)
            if i < 4:
                chevron_button = scraper._find_chevron_next_button()
                if chevron_button:
                    print("✅ Chevron button found")
                    if scraper._click_chevron_next():
                        print("✅ Chevron clicked")
                        time.sleep(2)
                    else:
                        print("❌ Chevron click failed")
                        break
                else:
                    print("❌ No chevron button found")
                    break
        
        return True
        
    except Exception as e:
        print(f"❌ Edge case test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            scraper.wd.quit()
        except:
            pass

def main():
    """Main test function"""
    print("🧪 COMPREHENSIVE CHEVRON NAVIGATION TESTING")
    print("=" * 60)
    
    # Test 1: Different scenarios
    print("Phase 1: Testing different bird species...")
    scenario_results = test_chevron_with_different_scenarios()
    
    # Test 2: Edge cases
    print("\nPhase 2: Testing edge cases...")
    edge_case_success = test_edge_cases()
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 FINAL SUMMARY")
    print(f"{'='*60}")
    
    print("\n🦅 Scenario Tests:")
    total_scenarios = len(scenario_results)
    successful_scenarios = sum(1 for r in scenario_results if r['success'])
    
    for result in scenario_results:
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"   {result['name']}: {status} ({result['actual']}/{result['expected']} images)")
    
    print(f"\n🔍 Edge Case Test: {'✅ PASS' if edge_case_success else '❌ FAIL'}")
    
    print(f"\n📈 Overall Results:")
    print(f"   Scenario Tests: {successful_scenarios}/{total_scenarios} passed")
    print(f"   Edge Case Test: {'Passed' if edge_case_success else 'Failed'}")
    
    overall_success = successful_scenarios == total_scenarios and edge_case_success
    
    if overall_success:
        print(f"\n🎉 ALL TESTS PASSED - Chevron navigation is working correctly!")
    else:
        print(f"\n⚠️ SOME TESTS FAILED - Check the logs above for issues")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
