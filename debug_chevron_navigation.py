#!/usr/bin/env python3
"""
Debug script untuk chevron navigation workflow
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def debug_chevron_workflow():
    """Debug chevron navigation workflow dengan logging detail"""
    print("🔍 DEBUG: Chevron Navigation Workflow")
    print("=" * 50)
    
    # Test URL - Java Sparrow photos
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo"
    output_dir = "debug_chevron_output"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Initialize scraper
        print("🚀 Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=False)
        
        # Navigate to URL
        print("🌐 Navigating to eBird URL...")
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Find first clickable image
        print("🔍 Finding first clickable image...")
        clickable_images = scraper._get_clickable_images_from_page()
        
        if not clickable_images:
            print("❌ No clickable images found")
            return False
            
        print(f"✅ Found {len(clickable_images)} clickable images")
        
        # Click first image to enter photo viewer
        first_image = clickable_images[0]
        print("📸 Clicking first image to enter photo viewer...")
        
        if not scraper._click_image_to_enter_viewer(first_image):
            print("❌ Failed to enter photo viewer")
            return False
            
        print("✅ Successfully entered photo viewer")
        
        # Debug: Check current URL and page state
        current_url = scraper.wd.current_url
        print(f"🔗 Current URL after entering viewer: {current_url}")
        
        # Process multiple images with detailed logging
        for i in range(3):  # Test with 3 images
            print(f"\n{'='*30}")
            print(f"🖼️ PROCESSING IMAGE {i+1}")
            print(f"{'='*30}")
            
            # Check if we're still in photo viewer
            if scraper._is_in_photo_viewer():
                print("✅ Still in photo viewer")
            else:
                print("❌ NOT in photo viewer - something went wrong!")
                current_url = scraper.wd.current_url
                print(f"🔗 Current URL: {current_url}")
                break
            
            # Take screenshot
            print(f"📸 Taking screenshot for image {i+1}...")
            screenshot_path = scraper._take_photo_viewer_screenshot(i, output_dir)
            
            if screenshot_path:
                print(f"✅ Screenshot saved: {os.path.basename(screenshot_path)}")
            else:
                print(f"❌ Screenshot failed for image {i+1}")
            
            # Check URL after screenshot
            current_url_after_screenshot = scraper.wd.current_url
            print(f"🔗 URL after screenshot: {current_url_after_screenshot}")
            
            if current_url != current_url_after_screenshot:
                print("⚠️ URL CHANGED after screenshot! This might be the problem.")
            
            # If this is not the last image, try to navigate to next
            if i < 2:  # Not the last iteration
                print(f"➡️ Attempting to navigate to next image...")
                
                # Check for chevron button
                chevron_button = scraper._find_chevron_next_button()
                if chevron_button:
                    print("✅ Chevron button found")
                    
                    # Click chevron
                    if scraper._click_chevron_next():
                        print("✅ Chevron clicked successfully")
                        time.sleep(3)  # Wait for navigation
                        
                        # Check URL after chevron click
                        current_url_after_chevron = scraper.wd.current_url
                        print(f"🔗 URL after chevron click: {current_url_after_chevron}")
                        
                        # Update current URL for next iteration
                        current_url = current_url_after_chevron
                        
                    else:
                        print("❌ Failed to click chevron")
                        break
                else:
                    print("❌ No chevron button found")
                    break
            
            # Brief pause between iterations
            time.sleep(2)
        
        print(f"\n✅ Debug completed!")
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            scraper.wd.quit()
        except:
            pass

def main():
    """Main debug function"""
    print("🔍 eBird Chevron Navigation Debug")
    print("=" * 50)
    
    success = debug_chevron_workflow()
    
    if success:
        print("\n✅ Debug COMPLETED - Check the logs above for issues")
    else:
        print("\n❌ Debug FAILED - Check the error messages above")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
