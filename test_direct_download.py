#!/usr/bin/env python3
"""
Test script untuk direct download functionality
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot, EBirdDirectDownloader

def test_direct_downloader_standalone():
    """Test direct downloader secara standalone"""
    print("🧪 Testing Direct Downloader (Standalone)")
    print("=" * 50)
    
    downloader = EBirdDirectDownloader()
    output_dir = "test_direct_standalone"
    os.makedirs(output_dir, exist_ok=True)
    
    # Test dengan asset IDs yang diketahui (contoh)
    test_cases = [
        {"asset_id": "230539581", "media_type": "photo"},
        {"asset_id": "180854341", "media_type": "photo"},
        {"asset_id": "123456789", "media_type": "photo"},  # Mungkin tidak ada
    ]
    
    results = []
    
    for i, test_case in enumerate(test_cases):
        print(f"\n📸 Test {i+1}: Asset ID {test_case['asset_id']}")
        
        result = downloader.download_media(
            asset_id=test_case['asset_id'],
            media_type=test_case['media_type'],
            output_dir=output_dir,
            index=i
        )
        
        results.append({
            "asset_id": test_case['asset_id'],
            "success": result is not None,
            "file": result
        })
    
    # Summary
    successful = sum(1 for r in results if r['success'])
    print(f"\n📊 Standalone Test Results:")
    print(f"   Total tests: {len(results)}")
    print(f"   Successful: {successful}")
    print(f"   Failed: {len(results) - successful}")
    
    return successful > 0

def test_integrated_direct_download():
    """Test direct download terintegrasi dengan scraper"""
    print("\n🧪 Testing Integrated Direct Download")
    print("=" * 50)
    
    # Test URL
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo"
    output_dir = "test_direct_integrated"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Initialize scraper
        print("🚀 Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=False)
        
        # Test dengan direct download method
        print("📸 Starting direct download test...")
        result = scraper.scrape_ebird(
            ebird_url=test_url,
            output_dir=output_dir,
            max_images=3,  # Small test batch
            method='click_and_view',
            timeout_minutes=10,
            max_load_more_clicks=0,  # No Load More clicks
            crop_to_bird=False,
            download_method='direct'  # Use direct download method
        )
        
        print(f"\n✅ Test completed! Processed {result} images")
        
        # Check results
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith(('.jpg', '.png'))]
            print(f"📁 Files created: {len(files)}")
            
            direct_downloads = [f for f in files if 'direct' in f]
            screenshots = [f for f in files if 'screenshot' in f or 'chevron' in f]
            
            print(f"   Direct downloads: {len(direct_downloads)}")
            print(f"   Screenshot fallbacks: {len(screenshots)}")
            
            for file in files:
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   - {file} ({file_size/1024:.1f} KB)")
        
        return result > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            scraper.wd.quit()
        except:
            pass

def test_asset_id_extraction():
    """Test asset ID extraction dari berbagai format URL"""
    print("\n🧪 Testing Asset ID Extraction")
    print("=" * 50)
    
    downloader = EBirdDirectDownloader()
    
    test_urls = [
        "https://macaulaylibrary.org/asset/230539581",
        "https://macaulaylibrary.org/asset/180854341#_ga=2.225998489",
        "https://macaulaylibrary.org/asset/180854341?_gl=1*v5o4c3",
        "https://macaulaylibrary.org/photo/106358811",
        "https://cdn.download.ams.birds.cornell.edu/api/v1/asset/230539581/2400",
        "Some text with asset/123456789 in it",
        "Invalid URL without asset ID"
    ]
    
    print("Testing asset ID extraction:")
    for url in test_urls:
        asset_id = downloader.extract_asset_id(url)
        status = "✅" if asset_id else "❌"
        print(f"   {status} {url[:50]}... → {asset_id}")
    
    return True

def compare_download_methods():
    """Compare direct download vs screenshot quality"""
    print("\n🧪 Comparing Download Methods")
    print("=" * 50)
    
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo"
    
    methods = [
        {"name": "Direct Download", "method": "direct", "dir": "compare_direct"},
        {"name": "Screenshot", "method": "screenshot", "dir": "compare_screenshot"}
    ]
    
    results = {}
    
    for method_info in methods:
        print(f"\n📸 Testing {method_info['name']}...")
        output_dir = method_info['dir']
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            scraper = EBirdScraperBot(headless=False)
            
            start_time = time.time()
            result = scraper.scrape_ebird(
                ebird_url=test_url,
                output_dir=output_dir,
                max_images=2,  # Small comparison
                method='click_and_view',
                timeout_minutes=5,
                max_load_more_clicks=0,
                download_method=method_info['method']
            )
            end_time = time.time()
            
            # Analyze results
            files = [f for f in os.listdir(output_dir) if f.endswith(('.jpg', '.png'))]
            total_size = sum(os.path.getsize(os.path.join(output_dir, f)) for f in files)
            
            results[method_info['name']] = {
                'files': len(files),
                'total_size_kb': total_size / 1024,
                'avg_size_kb': (total_size / len(files) / 1024) if files else 0,
                'time_seconds': end_time - start_time,
                'success': result > 0
            }
            
            scraper.wd.quit()
            
        except Exception as e:
            print(f"❌ {method_info['name']} failed: {e}")
            results[method_info['name']] = {'success': False, 'error': str(e)}
    
    # Print comparison
    print(f"\n📊 Method Comparison:")
    for method_name, result in results.items():
        if result.get('success'):
            print(f"   {method_name}:")
            print(f"     Files: {result['files']}")
            print(f"     Total size: {result['total_size_kb']:.1f} KB")
            print(f"     Avg size: {result['avg_size_kb']:.1f} KB")
            print(f"     Time: {result['time_seconds']:.1f} seconds")
        else:
            print(f"   {method_name}: FAILED")
    
    return True

def main():
    """Main test function"""
    print("🧪 DIRECT DOWNLOAD TESTING SUITE")
    print("=" * 60)
    
    tests = [
        ("Asset ID Extraction", test_asset_id_extraction),
        ("Standalone Direct Downloader", test_direct_downloader_standalone),
        ("Integrated Direct Download", test_integrated_direct_download),
        ("Method Comparison", compare_download_methods)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 Running: {test_name}")
        print(f"{'='*60}")
        
        try:
            success = test_func()
            results[test_name] = success
            print(f"✅ {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n📈 Overall Results: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED! Direct download is working correctly!")
    else:
        print(f"⚠️ Some tests failed. Check the logs above for details.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
