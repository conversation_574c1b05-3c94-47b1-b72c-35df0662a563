from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import os
import time
from urllib import request as wget
import tqdm as tqdm
import argparse
import requests
import json
from urllib.parse import urljoin, urlparse
from PIL import Image, ImageDraw
import cv2
import numpy as np
'''

wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'utils\\chromedriver.exe'))
wd.get('https://google.com')
search = wd.find_element_by_css_selector('input.gLFyf')
search.send_keys('')

'''

class ScraperBot():
    def __init__(self):
        self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'))
        self.urlbase = "https://google.com/search?tbm=isch&q={}"

    def _initiateSession(self, key):
        self.wd.get(self.urlbase.format(key))

    def _acceptCookies(self):
        try:
            self.wd.execute_script("document.getElementsByClassName('USRMqe')[0].style.display = 'none';")
        except:
            pass

    def _endOfPage(self):
        try:
            self.wd.find_element_by_class_name('OuJzKb Yu2Dnd')
            print("no more files")
        except:
            pass

        try:
            self.wd.find_element_by_class_name('mye4qd').click()
            time.sleep(1)
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        except:
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")


    def _deduplicate(self, listOfurls):
        try:
            inputList = list(set(listOfurls))
            return inputList
        except:
            pass

    def _checkOpenTabs(self):
        browserTabs = self.wd.window_handles
        if len(browserTabs) > 1:
            self.wd.switch_to.window(browserTabs[1])
            self.wd.close()
            self.wd.switch_to.window(browserTabs[0])

    def _getURL(self):
        thumbs = self.wd.find_elements_by_css_selector('img.Q4LuWd')
        urls = []
        for thumbImg in thumbs:
            try:
                thumbImg.click()
                actualImg = self.wd.find_elements_by_css_selector('img.n3VNCb')

                for imageData in actualImg:
                    if 'https' in imageData.get_attribute('src'):
                        urls.append(imageData.get_attribute('src'))

                self._checkOpenTabs()
            except:
                pass
        return urls

    def _totalImages(self, dir):
        count = 0
        for filename in os.listdir(dir):
            if filename.endswith('.jpg'):
                count += 1
            else:
                continue
        return count

    def _downloader(self, data, key, out_dir):
        key = key.replace(" ", "_")
        DIR1 = os.path.join(out_dir, key)

        try:
            os.mkdir(DIR1)
        except:
            pass

        for idx in tqdm.tqdm(range(len(data))):
            filename = "{}-{}.jpg".format(key, idx)
            PATH = os.path.join(DIR1, '{}'.format(filename))

            try:
                print("downloading next batch")
                wget.urlretrieve(str(data[idx]), PATH)
            except:
                pass


    def scrape(self, search, min_image_count, directory):
        self._initiateSession(key=search)
        self._acceptCookies()

        totalImageCount = 0
        while totalImageCount < min_image_count:
            urlList = self._deduplicate(self._getURL())
            self._downloader(data=urlList,
                             key=search,
                             out_dir=directory)
            urlList.clear()
            totalImageCount = self._totalImages(os.path.join(os.getcwd(), search.replace(" ", "_")))
            print("current Image count: {}".format(totalImageCount))
            self._endOfPage()
            time.sleep(2)

        if totalImageCount >= min_image_count:
            self.wd.quit()


class EBirdScraperBot():
    def __init__(self, headless=False, custom_resolution=None):
        # Initialize ultra quality mode
        self.ultra_quality_mode = False
        self.custom_resolution = custom_resolution or (1920, 1080)

        # Asset ID tracking system for unique image validation
        self._downloaded_asset_ids = set()
        self._current_session_stats = {
            'attempted': 0,
            'unique_downloads': 0,
            'duplicates_skipped': 0,
            'failed_detections': 0
        }
        self._original_gallery_url = None

        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")

        # Enhanced window size for better screenshots
        width, height = self.custom_resolution
        chrome_options.add_argument(f"--window-size={width},{height}")
        chrome_options.add_argument("--start-maximized")

        # High quality rendering options
        chrome_options.add_argument("--force-device-scale-factor=1")
        chrome_options.add_argument("--high-dpi-support=1")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")

        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Gunakan webdriver-manager untuk otomatis download ChromeDriver
        try:
            service = Service(ChromeDriverManager().install())
            self.wd = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            print(f"Error initializing Chrome driver: {e}")
            # Fallback ke cara lama jika webdriver-manager gagal
            try:
                self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'), options=chrome_options)
            except:
                self.wd = webdriver.Chrome(options=chrome_options)

        # Disable webdriver detection
        self.wd.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set maximum window size for better screenshots
        self.wd.maximize_window()

        self.wait = WebDriverWait(self.wd, 10)
        self.base_url = "https://media.ebird.org"

    def _scroll_and_load_more(self, timeout_seconds=300, max_load_more_clicks=None):
        """Enhanced load more functionality with configurable click limits"""
        if max_load_more_clicks is None:
            print("🔄 Starting UNLIMITED LOAD MORE process...")
            print("📋 Will load ALL available content until no more buttons found")
        else:
            print(f"🔄 Starting LIMITED LOAD MORE process...")
            print(f"📋 Will click 'More results' button maximum {max_load_more_clicks} times")

        start_time = time.time()
        load_more_clicks = 0
        consecutive_no_change = 0
        max_consecutive_no_change = 5  # Increased for more patience

        # Simple tracking for "More results" button only
        more_results_clicks = 0
        max_ineffective_clicks = 3  # Stop after 3 clicks without new content

        # Check if we have a click limit
        has_click_limit = max_load_more_clicks is not None
        if has_click_limit:
            print(f"🎯 Click limit: {max_load_more_clicks} 'More results' clicks maximum")

        # Initial state
        last_height = self.wd.execute_script("return document.body.scrollHeight")
        last_image_count = len(self._get_current_image_elements())

        print(f"📊 Initial state: {last_image_count} images, page height: {last_height}px")

        while (time.time() - start_time) < timeout_seconds:
            cycle_start = time.time()

            # Step 1: Scroll to bottom to trigger any lazy loading
            self._scroll_to_bottom_gradually()

            # Step 2: Check if we've reached the click limit
            if has_click_limit and load_more_clicks >= max_load_more_clicks:
                print(f"🎯 Reached click limit: {max_load_more_clicks} 'More results' clicks completed")
                print("🏁 Load More process complete - click limit reached!")
                break

            # Step 3: Simple "More results" button detection and clicking
            more_results_found = self._find_and_click_more_results_button()

            if more_results_found:
                load_more_clicks += 1
                more_results_clicks += 1
                print(f"✅ 'More results' clicked #{load_more_clicks}/{max_load_more_clicks if has_click_limit else '∞'} - Waiting for new content...")

                # Wait for content to load with progress monitoring
                self._wait_for_content_loading()

                # Reset no-change counter since we found and clicked a button
                consecutive_no_change = 0
            else:
                print("🔍 No 'More results' button found in this cycle")

            # Step 3: Check for content changes
            new_height = self.wd.execute_script("return document.body.scrollHeight")
            current_image_count = len(self._get_current_image_elements())

            # Progress feedback
            height_change = new_height - last_height
            image_change = current_image_count - last_image_count

            print(f"📈 Progress: Height {last_height}→{new_height} (+{height_change}), Images {last_image_count}→{current_image_count} (+{image_change})")

            # Step 4: Determine if we should continue
            if new_height == last_height and current_image_count == last_image_count:
                consecutive_no_change += 1
                print(f"⏸️ No changes detected ({consecutive_no_change}/{max_consecutive_no_change})")

                # If we just clicked "More results" but got no new content
                if more_results_found:
                    print(f"⚠️ 'More results' clicked but no new content loaded")
                    # If "More results" button clicked multiple times without effect, stop
                    if consecutive_no_change >= max_ineffective_clicks:
                        print("🛑 'More results' button appears to be ineffective - stopping")
                        break

                if consecutive_no_change >= max_consecutive_no_change:
                    print("🏁 No more content available - Load More process complete!")
                    break
            else:
                consecutive_no_change = 0  # Reset counter
                print("✅ New content detected, continuing...")

                # Reset ineffective click counter when new content is successfully loaded
                if more_results_found:
                    more_results_clicks = 0  # Reset counter for successful load

            # Update state for next iteration
            last_height = new_height
            last_image_count = current_image_count

            # Cycle timing info
            cycle_time = time.time() - cycle_start
            print(f"⏱️ Cycle completed in {cycle_time:.1f}s")

            # Brief pause between cycles
            time.sleep(2)

        # Final summary
        total_time = time.time() - start_time
        final_image_count = len(self._get_current_image_elements())

        print("=" * 60)
        print("🎊 CONTINUOUS LOAD MORE COMPLETED!")
        print(f"⏱️ Total time: {total_time:.1f} seconds")
        print(f"🔄 Load More clicks: {load_more_clicks}")
        print(f"📸 Final image count: {final_image_count}")
        print(f"📈 Images loaded: {final_image_count - (last_image_count if 'last_image_count' in locals() else 0)}")
        print("=" * 60)

    def _scroll_to_bottom_gradually(self):
        """Gradually scroll to bottom to trigger lazy loading"""
        try:
            # Get current scroll position and total height
            current_scroll = self.wd.execute_script("return window.pageYOffset")
            total_height = self.wd.execute_script("return document.body.scrollHeight")

            # If not at bottom, scroll gradually
            if current_scroll < total_height - 1000:  # 1000px buffer
                # Scroll in steps to trigger lazy loading
                steps = 3
                step_size = (total_height - current_scroll) // steps

                for i in range(steps):
                    scroll_to = current_scroll + (step_size * (i + 1))
                    self.wd.execute_script(f"window.scrollTo(0, {scroll_to});")
                    time.sleep(1)  # Wait for lazy loading

                # Final scroll to absolute bottom
                self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

        except Exception as e:
            print(f"⚠️ Error in gradual scroll: {e}")
            # Fallback to simple scroll
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

    def _continuous_load_more_detection(self):
        """Continuously detect and click Load More buttons with multiple strategies"""
        max_detection_attempts = 3

        for attempt in range(max_detection_attempts):
            print(f"🔍 Load More detection attempt {attempt + 1}/{max_detection_attempts}")

            # Strategy 1: Enhanced button detection
            if self._enhanced_load_more_detection():
                return True

            # Strategy 2: Scroll-triggered detection (sometimes buttons appear after scroll)
            if attempt < max_detection_attempts - 1:  # Don't scroll on last attempt
                self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                if self._enhanced_load_more_detection():
                    return True

            # Strategy 3: Wait and retry (for dynamic content)
            if attempt < max_detection_attempts - 1:
                print(f"⏳ Waiting 3s before retry...")
                time.sleep(3)

        return False

    def _continuous_load_more_detection_with_tracking(self, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Enhanced Load More detection with button click tracking to prevent infinite loops"""
        max_detection_attempts = 3

        for attempt in range(max_detection_attempts):
            print(f"🔍 Load More detection attempt {attempt + 1}/{max_detection_attempts}")

            # Strategy 1: Enhanced button detection with tracking
            result = self._enhanced_load_more_detection_with_tracking(clicked_buttons, max_same_button_clicks, button_effectiveness)
            if result['found'] or result['reason'] in ['max_clicks_reached', 'ineffective_button']:
                return result

            # Strategy 2: Scroll-triggered detection (sometimes buttons appear after scroll)
            if attempt < max_detection_attempts - 1:  # Don't scroll on last attempt
                self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                result = self._enhanced_load_more_detection_with_tracking(clicked_buttons, max_same_button_clicks, button_effectiveness)
                if result['found'] or result['reason'] in ['max_clicks_reached', 'ineffective_button']:
                    return result

            # Strategy 3: Wait and retry (for dynamic content)
            if attempt < max_detection_attempts - 1:
                print(f"⏳ Waiting 2s before retry...")  # Reduced wait time
                time.sleep(2)

        return {'found': False, 'reason': 'not_found', 'button_text': None}

    def _enhanced_load_more_detection(self):
        """Enhanced Load More button detection with multiple selectors and strategies"""
        # Comprehensive list of Load More button selectors
        load_more_selectors = [
            # eBird specific selectors
            'button[data-testid="load-more"]',
            'button[data-testid="load-more-button"]',
            'button[data-testid="show-more"]',
            '[data-testid*="load"]',
            '[data-testid*="more"]',

            # Generic Load More patterns
            'button:contains("Load More")',
            'button:contains("Show More")',
            'button:contains("Load more")',
            'button:contains("Show more")',
            'button:contains("More")',
            'button:contains("See More")',
            'button:contains("View More")',

            # Class-based selectors
            '.load-more-button',
            '.show-more-button',
            '.load-more',
            '.show-more',
            'button[class*="load"]',
            'button[class*="more"]',
            'button[class*="Load"]',
            'button[class*="More"]',

            # Aria labels and accessibility
            'button[aria-label*="more"]',
            'button[aria-label*="load"]',
            'button[aria-label*="More"]',
            'button[aria-label*="Load"]',
            '[role="button"][aria-label*="more"]',
            '[role="button"][aria-label*="load"]',

            # Pagination and navigation
            'a[href*="offset"]',
            'a[href*="page"]',
            '.pagination a:last-child',
            '.pagination button:last-child',
            'button[data-page]',

            # Generic button patterns that might be Load More
            'button[type="button"]:contains("more")',
            'button[type="button"]:contains("load")',
            'div[role="button"]:contains("more")',
            'div[role="button"]:contains("load")'
        ]

        for selector in load_more_selectors:
            try:
                if self._try_selector_for_load_more(selector):
                    return True
            except Exception as e:
                continue

        return False

    def _enhanced_load_more_detection_with_tracking(self, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Simple Load More button detection focused only on 'More results' button"""
        # Focus only on "More results" button - the one that actually works
        target_selectors = [
            'button:contains("More results")',  # Primary target
            'button:contains("More")',          # Fallback
        ]

        for selector in target_selectors:
            try:
                result = self._try_selector_for_load_more_with_tracking(selector, clicked_buttons, max_same_button_clicks, button_effectiveness)
                if result['found']:
                    return result
                elif result['reason'] in ['max_clicks_reached', 'ineffective_button']:
                    # If "More results" button is ineffective, stop completely
                    if "More results" in result['button_text']:
                        print(f"🛑 'More results' button is no longer effective - stopping")
                        return result
            except Exception as e:
                continue

        return {'found': False, 'reason': 'not_found', 'button_text': None}

    def _find_and_click_more_results_button(self):
        """Simple method to find and click only 'More results' button"""
        try:
            # Look for buttons containing "More results" text
            buttons = self.wd.find_elements(By.TAG_NAME, 'button')

            for button in buttons:
                try:
                    button_text = button.text.strip().lower()

                    # Only click if it's exactly "More results" or "More"
                    if "more results" in button_text or button_text == "more":
                        # Check if button is clickable
                        if button.is_displayed() and button.is_enabled():
                            print(f"✅ Found target button: '{button.text.strip()}'")

                            # Scroll to button and click
                            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button)
                            time.sleep(1)

                            try:
                                button.click()
                                print("✅ 'More results' clicked successfully")
                                return True
                            except Exception as click_error:
                                # Try JavaScript click as fallback
                                self.wd.execute_script("arguments[0].click();", button)
                                print("✅ 'More results' clicked successfully (JavaScript)")
                                return True

                except Exception as e:
                    continue

            print("🔍 No 'More results' button found")
            return False

        except Exception as e:
            print(f"❌ Error finding 'More results' button: {e}")
            return False

    def _try_selector_for_load_more(self, selector):
        """Try a specific selector to find and click Load More button"""
        try:
            # Handle :contains() pseudo-selector manually
            if ':contains(' in selector:
                return self._handle_contains_selector(selector)
            else:
                return self._handle_css_selector(selector)
        except Exception as e:
            return False

    def _try_selector_for_load_more_with_tracking(self, selector, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Try a specific selector to find and click Load More button with tracking"""
        try:
            # Handle :contains() pseudo-selector manually
            if ':contains(' in selector:
                return self._handle_contains_selector_with_tracking(selector, clicked_buttons, max_same_button_clicks, button_effectiveness)
            else:
                return self._handle_css_selector_with_tracking(selector, clicked_buttons, max_same_button_clicks, button_effectiveness)
        except Exception as e:
            return {'found': False, 'reason': 'error', 'button_text': None}

    def _handle_contains_selector_with_tracking(self, selector, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Handle selectors with :contains() pseudo-selector with tracking"""
        try:
            # Extract text to find and base selector
            parts = selector.split(':contains("')
            base_selector = parts[0]
            text_to_find = parts[1].split('")')[0]

            # Find elements by base selector
            if base_selector:
                elements = self.wd.find_elements(By.CSS_SELECTOR, base_selector)
            else:
                elements = self.wd.find_elements(By.TAG_NAME, 'button')

            for element in elements:
                if (text_to_find.lower() in element.text.lower() and
                    self._is_valid_load_more_button(element)):

                    button_text = element.text.strip()

                    # Check if this button is known to be ineffective
                    if button_text in button_effectiveness and button_effectiveness[button_text] == False:
                        print(f"⚠️ Button '{button_text}' is known to be ineffective, skipping")
                        return {'found': False, 'reason': 'ineffective_button', 'button_text': button_text}

                    # Check if this button has been clicked too many times
                    if button_text in clicked_buttons:
                        if clicked_buttons[button_text] >= max_same_button_clicks:
                            print(f"⚠️ Button '{button_text}' already clicked {clicked_buttons[button_text]} times, skipping")
                            button_effectiveness[button_text] = False  # Mark as ineffective
                            return {'found': False, 'reason': 'max_clicks_reached', 'button_text': button_text}

                    print(f"✅ Found Load More button: '{button_text}' (selector: {selector})")

                    # Try to click the button
                    if self._click_load_more_element(element):
                        # Track the click
                        clicked_buttons[button_text] = clicked_buttons.get(button_text, 0) + 1
                        return {'found': True, 'reason': 'clicked', 'button_text': button_text}

            return {'found': False, 'reason': 'not_found', 'button_text': None}

        except Exception as e:
            return {'found': False, 'reason': 'error', 'button_text': None}

    def _handle_css_selector_with_tracking(self, selector, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Handle regular CSS selectors with tracking"""
        try:
            elements = self.wd.find_elements(By.CSS_SELECTOR, selector)

            for element in elements:
                if self._is_valid_load_more_button(element):
                    button_text = element.text.strip() or element.get_attribute('aria-label') or 'Load More'

                    # Check if this button is known to be ineffective
                    if button_text in button_effectiveness and button_effectiveness[button_text] == False:
                        print(f"⚠️ Button '{button_text}' is known to be ineffective, skipping")
                        return {'found': False, 'reason': 'ineffective_button', 'button_text': button_text}

                    # Check if this button has been clicked too many times
                    if button_text in clicked_buttons:
                        if clicked_buttons[button_text] >= max_same_button_clicks:
                            print(f"⚠️ Button '{button_text}' already clicked {clicked_buttons[button_text]} times, skipping")
                            button_effectiveness[button_text] = False  # Mark as ineffective
                            return {'found': False, 'reason': 'max_clicks_reached', 'button_text': button_text}

                    print(f"✅ Found Load More element: '{button_text}' (selector: {selector})")

                    # Try to click the button
                    if self._click_load_more_element(element):
                        # Track the click
                        clicked_buttons[button_text] = clicked_buttons.get(button_text, 0) + 1
                        return {'found': True, 'reason': 'clicked', 'button_text': button_text}

            return {'found': False, 'reason': 'not_found', 'button_text': None}

        except Exception as e:
            return {'found': False, 'reason': 'error', 'button_text': None}

    def _handle_contains_selector(self, selector):
        """Handle selectors with :contains() pseudo-selector"""
        try:
            # Extract text to find and base selector
            parts = selector.split(':contains("')
            base_selector = parts[0]
            text_to_find = parts[1].split('")')[0]

            # Find elements by base selector
            if base_selector:
                elements = self.wd.find_elements(By.CSS_SELECTOR, base_selector)
            else:
                elements = self.wd.find_elements(By.TAG_NAME, 'button')

            for element in elements:
                if (text_to_find.lower() in element.text.lower() and
                    self._is_valid_load_more_button(element)):

                    print(f"✅ Found Load More button: '{element.text.strip()}' (selector: {selector})")
                    return self._click_load_more_element(element)

            return False

        except Exception as e:
            return False

    def _handle_css_selector(self, selector):
        """Handle regular CSS selectors"""
        try:
            elements = self.wd.find_elements(By.CSS_SELECTOR, selector)

            for element in elements:
                if self._is_valid_load_more_button(element):
                    button_text = element.text.strip() or element.get_attribute('aria-label') or 'Load More'
                    print(f"✅ Found Load More element: '{button_text}' (selector: {selector})")
                    return self._click_load_more_element(element)

            return False

        except Exception as e:
            return False

    def _is_valid_load_more_button(self, element):
        """Validate if an element is a valid Load More button"""
        try:
            # Must be displayed and enabled
            if not (element.is_displayed() and element.is_enabled()):
                return False

            # Check if element is in viewport or can be scrolled to
            try:
                location = element.location
                size = element.size
                if location['y'] < -1000 or location['y'] > 10000:  # Reasonable bounds
                    return False
            except:
                pass

            # Additional validation for text content
            text = element.text.lower()
            aria_label = (element.get_attribute('aria-label') or '').lower()

            # Must contain relevant keywords
            relevant_keywords = ['more', 'load', 'show', 'next', 'continue']
            has_relevant_text = any(keyword in text or keyword in aria_label
                                  for keyword in relevant_keywords)

            # Exclude irrelevant buttons
            exclude_keywords = ['less', 'hide', 'close', 'back', 'previous', 'cancel']
            has_exclude_text = any(keyword in text or keyword in aria_label
                                 for keyword in exclude_keywords)

            # Only accept "More results" or "More" buttons - be very specific
            if "more results" in text.lower():
                return True  # This is exactly what we want
            elif text.lower().strip() == "more":
                return True  # Simple "More" button is also acceptable
            else:
                # Reject everything else to avoid clicking wrong buttons
                print(f"⚠️ Skipping non-target button: '{text.strip()}'")
                return False

        except Exception as e:
            return False

    def _click_load_more_element(self, element):
        """Click a Load More element with multiple strategies"""
        try:
            # Strategy 1: Scroll element into view
            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
            time.sleep(1)

            # Strategy 2: Try regular click
            try:
                element.click()
                print("✅ Load More clicked successfully (regular click)")
                return True
            except Exception as click_error:
                print(f"⚠️ Regular click failed: {click_error}")

            # Strategy 3: Try JavaScript click
            try:
                self.wd.execute_script("arguments[0].click();", element)
                print("✅ Load More clicked successfully (JavaScript click)")
                return True
            except Exception as js_error:
                print(f"⚠️ JavaScript click failed: {js_error}")

            # Strategy 4: Try WebDriverWait + click
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                WebDriverWait(self.wd, 5).until(EC.element_to_be_clickable(element))
                element.click()
                print("✅ Load More clicked successfully (WebDriverWait click)")
                return True
            except Exception as wait_error:
                print(f"⚠️ WebDriverWait click failed: {wait_error}")

            return False

        except Exception as e:
            print(f"❌ All click strategies failed: {e}")
            return False

    def _wait_for_content_loading(self):
        """Wait for content to load after clicking Load More with progress monitoring"""
        print("⏳ Monitoring content loading...")

        initial_height = self.wd.execute_script("return document.body.scrollHeight")
        initial_images = len(self._get_current_image_elements())

        max_wait_time = 10  # Reduced wait time to prevent hanging on ineffective buttons
        check_interval = 1   # Check every second
        stable_count = 0     # Count of stable checks
        required_stable = 3  # Required stable checks before considering loaded

        for i in range(max_wait_time):
            time.sleep(check_interval)

            current_height = self.wd.execute_script("return document.body.scrollHeight")
            current_images = len(self._get_current_image_elements())

            height_change = current_height - initial_height
            image_change = current_images - initial_images

            if height_change > 0 or image_change > 0:
                print(f"📈 Loading progress: +{height_change}px height, +{image_change} images")
                stable_count = 0  # Reset stable count
                initial_height = current_height  # Update baseline
                initial_images = current_images
            else:
                stable_count += 1
                if stable_count >= required_stable:
                    print(f"✅ Content loading complete (stable for {stable_count}s)")
                    break

        # Final scroll to ensure all content is visible
        self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)

    def _get_current_image_elements(self):
        """Get current count of image elements on the page"""
        try:
            selectors = [
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',
                'img[data-src*="macaulaylibrary.org"]',
                'img[data-src*="cdn.download.ams.birds.cornell.edu"]',
                '.MediaCard img',
                '.media-card img',
                '.photo-card img'
            ]

            all_images = []
            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    all_images.extend(images)
                except:
                    continue

            # Remove duplicates based on src attribute
            unique_images = []
            seen_srcs = set()
            for img in all_images:
                src = img.get_attribute('src') or img.get_attribute('data-src')
                if src and src not in seen_srcs:
                    unique_images.append(img)
                    seen_srcs.add(src)

            return unique_images
        except Exception as e:
            print(f"Error getting current image elements: {e}")
            return []

    def _click_load_more_button(self):
        """Legacy method - now uses enhanced load more detection"""
        print("🔄 Using enhanced Load More detection...")
        return self._enhanced_load_more_detection()

    def _get_clickable_images_from_page(self):
        """Cari semua gambar yang bisa diklik untuk melihat versi penuh"""
        clickable_images = []

        try:
            # Tunggu sampai halaman dimuat
            time.sleep(3)

            # Cari elemen gambar yang bisa diklik (biasanya dalam link atau card)
            selectors = [
                "a[href*='/catalog/'] img",  # Gambar dalam link catalog
                ".MediaCard img",            # Gambar dalam MediaCard
                ".MediaThumbnail img",       # Gambar thumbnail
                "[data-testid='media-card'] img",  # Gambar dalam media card
                "img[src*='macaulaylibrary.org']",  # Gambar langsung
                "img[src*='cdn.download.ams.birds.cornell.edu']"  # Gambar CDN
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    print(f"Found {len(images)} clickable images with selector: {selector}")

                    for img in images:
                        # Cek apakah gambar bisa diklik (ada parent link atau clickable)
                        parent_link = None
                        try:
                            # Cari parent link
                            parent_link = img.find_element(By.XPATH, "./ancestor::a[1]")
                        except:
                            # Jika tidak ada parent link, coba klik gambar langsung
                            pass

                        clickable_images.append({
                            'element': img,
                            'parent_link': parent_link,
                            'src': img.get_attribute('src') or img.get_attribute('data-src')
                        })

                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue

            print(f"Total clickable images found: {len(clickable_images)}")
            return clickable_images

        except Exception as e:
            print(f"Error finding clickable images: {e}")
            return []



    def _click_image_to_enter_viewer(self, clickable_img):
        """Click the first image to enter photo viewer"""
        try:
            print(f"🖱️ Clicking first image to enter photo viewer...")

            # Scroll element into view
            self.wd.execute_script("""
                arguments[0].scrollIntoView({
                    behavior: 'smooth',
                    block: 'center',
                    inline: 'center'
                });
            """, clickable_img['element'])
            time.sleep(2)

            # Click the image to enter photo viewer
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            try:
                if clickable_img['parent_link']:
                    WebDriverWait(self.wd, 10).until(
                        EC.element_to_be_clickable(clickable_img['parent_link'])
                    )
                    clickable_img['parent_link'].click()
                else:
                    WebDriverWait(self.wd, 10).until(
                        EC.element_to_be_clickable(clickable_img['element'])
                    )
                    clickable_img['element'].click()
            except Exception as click_error:
                print(f"⚠️ Click failed, trying JavaScript click: {click_error}")
                element_to_click = clickable_img['parent_link'] or clickable_img['element']
                self.wd.execute_script("arguments[0].click();", element_to_click)

            # Wait for photo viewer to load
            print(f"⏳ Waiting for photo viewer to load...")
            time.sleep(4)

            # Verify we're in photo viewer
            if self._is_in_photo_viewer():
                print(f"✅ Successfully entered photo viewer")
                return True
            else:
                print(f"❌ Failed to enter photo viewer")
                return False

        except Exception as e:
            print(f"❌ Error entering photo viewer: {e}")
            return False

    def _is_in_photo_viewer(self):
        """Check if we're currently in the photo viewer"""
        try:
            # Wait a moment for page to load
            time.sleep(2)

            # Look for photo viewer indicators
            viewer_selectors = [
                # eBird photo viewer elements
                '[data-testid="media-viewer"]',
                '.MediaViewer',
                '.PhotoViewer',
                '.FullscreenViewer',

                # Navigation elements that indicate photo viewer
                'button[aria-label*="next" i]',
                'button[aria-label*="previous" i]',
                'button[title*="next" i]',
                'button[title*="previous" i]',

                # Close buttons (indicates modal/viewer is open)
                'button[aria-label*="close" i]:not([style*="display: none"])',
                'button[title*="close" i]:not([style*="display: none"])',

                # eBird specific modal indicators
                '.Modal',
                '.modal',
                '[role="dialog"]',

                # Large image containers
                '.MediaDisplay',
                '.FullscreenImage'
            ]

            print(f"   🔍 Checking for photo viewer indicators...")
            for selector in viewer_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            print(f"   ✅ Photo viewer detected via: {selector}")
                            return True
                except:
                    continue

            # Check URL for photo viewer indicators
            current_url = self.wd.current_url
            print(f"   🔗 Current URL: {current_url}")
            if any(indicator in current_url.lower() for indicator in ['modal', 'viewer', 'fullscreen', 'asset', 'media']):
                print(f"   ✅ Photo viewer detected via URL change")
                return True

            # Check for large images that might indicate photo viewer
            try:
                large_images = self.wd.find_elements(By.CSS_SELECTOR, 'img[src*="macaulaylibrary.org"]')
                for img in large_images:
                    if img.is_displayed():
                        size = img.size
                        if size['width'] > 400 and size['height'] > 300:  # Reasonably large image
                            print(f"   ✅ Photo viewer detected via large image: {size['width']}x{size['height']}")
                            return True
            except:
                pass

            print(f"   ⚠️ No photo viewer indicators found")
            return False

        except Exception as e:
            print(f"⚠️ Error checking photo viewer status: {e}")
            return False

    def _find_chevron_next_button(self):
        """Find the next/forward chevron button in photo viewer"""
        try:
            # Multiple selector strategies for chevron/next buttons
            chevron_selectors = [
                # Standard next button selectors
                'button[aria-label*="next" i]',
                'button[title*="next" i]',
                'button[aria-label*="forward" i]',
                'button[title*="forward" i]',

                # Chevron character selectors
                'button:contains("›")',
                'button:contains(">")',
                'button:contains("▶")',
                'button:contains("→")',

                # eBird specific selectors
                '[data-testid="next-button"]',
                '[data-testid="media-next"]',
                '.next-button',
                '.media-next',
                '.chevron-right',
                '.arrow-right',

                # Generic navigation selectors
                'button[class*="next"]',
                'button[class*="forward"]',
                'button[class*="right"]',
                'button[class*="chevron"]',

                # SVG-based navigation
                'button svg[class*="chevron"]',
                'button svg[class*="arrow"]',
                'button svg[class*="next"]'
            ]

            for selector in chevron_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            # Additional validation - make sure it's not a close button
                            aria_label = element.get_attribute('aria-label') or ''
                            title = element.get_attribute('title') or ''
                            text = element.text or ''

                            # Skip if it's a close button
                            if any(word in (aria_label + title + text).lower() for word in ['close', 'exit', 'dismiss']):
                                continue

                            print(f"✅ Found chevron next button: {selector}")
                            return element
                except Exception as e:
                    continue

            print("⚠️ No chevron next button found")
            return None

        except Exception as e:
            print(f"❌ Error finding chevron button: {e}")
            return None

    def _click_chevron_next(self):
        """Click the next/forward chevron button"""
        try:
            chevron_button = self._find_chevron_next_button()

            if not chevron_button:
                return False

            # Scroll button into view
            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", chevron_button)
            time.sleep(1)

            # Try clicking the chevron button
            try:
                chevron_button.click()
                print("✅ Chevron next button clicked successfully")
                time.sleep(2)  # Wait for navigation
                return True
            except Exception as click_error:
                print(f"⚠️ Regular click failed, trying JavaScript click: {click_error}")
                # Fallback to JavaScript click
                self.wd.execute_script("arguments[0].click();", chevron_button)
                print("✅ Chevron next button clicked via JavaScript")
                time.sleep(2)  # Wait for navigation
                return True

        except Exception as e:
            print(f"❌ Error clicking chevron button: {e}")
            return False

    def _process_images_with_chevron_navigation(self, output_dir, max_images, stats, timeout_seconds, start_time):
        """Process images using chevron navigation within photo viewer"""
        try:
            processed_count = 0

            print(f"\n🔄 Starting chevron navigation workflow...")

            while processed_count < max_images:
                # Check timeout
                if time.time() - start_time > timeout_seconds:
                    print(f"⏰ Timeout reached after {timeout_seconds/60:.1f} minutes")
                    break

                print(f"\n📸 Processing image {processed_count + 1}/{max_images}...")

                # Take screenshot of current image
                result = self._take_photo_viewer_screenshot(processed_count, output_dir)

                if result:
                    stats['successful_screenshots'] += 1
                    print(f"✅ Image {processed_count + 1} screenshot: {os.path.basename(result)}")
                    processed_count += 1
                else:
                    stats['failed_attempts'] += 1
                    print(f"❌ Screenshot failed for image {processed_count + 1}")
                    # Continue to next image even if screenshot failed
                    processed_count += 1

                # If we've reached max images, stop
                if processed_count >= max_images:
                    print(f"🎯 Reached maximum images limit ({max_images})")
                    break

                # Try to navigate to next image using chevron
                print(f"➡️ Attempting to navigate to next image...")
                if self._click_chevron_next():
                    print(f"✅ Successfully navigated to next image")
                    time.sleep(2)  # Wait for new image to load
                else:
                    print(f"🏁 No more chevron button found - reached end of images")
                    break

            print(f"\n🏁 Chevron navigation completed. Processed {processed_count} images.")
            return processed_count

        except Exception as e:
            print(f"❌ Error in chevron navigation workflow: {e}")
            import traceback
            traceback.print_exc()
            return processed_count if 'processed_count' in locals() else 0

    def _take_photo_viewer_screenshot(self, index, output_dir):
        """Take screenshot of the current image in photo viewer"""
        try:
            print(f"   📸 Taking screenshot in photo viewer...")

            # Wait for image to fully load
            time.sleep(2)

            # Find the best image element to screenshot
            screenshot_element = self._find_photo_viewer_image_element()

            if screenshot_element:
                # Generate filename
                screenshot_filename = f"ebird_chevron_{index + 1:03d}.png"
                screenshot_path = os.path.join(output_dir, screenshot_filename)

                # Take screenshot of the image element
                success = self._take_ultra_quality_screenshot(screenshot_element, screenshot_path, crop_to_bird=False)

                if success:
                    file_size = os.path.getsize(screenshot_path)
                    print(f"   ✅ Screenshot saved: {screenshot_filename}")
                    print(f"   📊 File size: {file_size/1024:.1f} KB")
                    return screenshot_path
                else:
                    print(f"   ⚠️ Element screenshot failed, trying full page...")
                    # Fallback to full page screenshot
                    self.wd.save_screenshot(screenshot_path)
                    if os.path.exists(screenshot_path):
                        print(f"   ✅ Full page screenshot saved: {screenshot_filename}")
                        return screenshot_path
            else:
                print(f"   ⚠️ No suitable image element found, taking full page screenshot...")
                # Fallback to full page screenshot
                screenshot_filename = f"ebird_chevron_{index + 1:03d}.png"
                screenshot_path = os.path.join(output_dir, screenshot_filename)
                self.wd.save_screenshot(screenshot_path)
                if os.path.exists(screenshot_path):
                    print(f"   ✅ Full page screenshot saved: {screenshot_filename}")
                    return screenshot_path

            print(f"   ❌ All screenshot methods failed")
            return None

        except Exception as e:
            print(f"❌ Error taking photo viewer screenshot: {e}")
            return None

    def _find_photo_viewer_image_element(self):
        """Find the main image element in photo viewer for screenshot"""
        try:
            # Selectors for main image in photo viewer
            image_selectors = [
                # eBird specific photo viewer images
                '[data-testid="media-viewer"] img',
                '.MediaViewer img',
                '.PhotoViewer img',
                '.FullscreenViewer img',

                # High-resolution image selectors
                'img[src*="macaulaylibrary.org"]:not([src*="thumbnail"])',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',

                # Generic large image selectors
                'img[width][height]',
                'img[style*="width"]',

                # Fallback to any visible image
                'img'
            ]

            best_element = None
            best_size = 0

            for selector in image_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate element size
                            size = element.size
                            element_area = size['width'] * size['height']

                            # Prefer larger images
                            if element_area > best_size:
                                best_element = element
                                best_size = element_area

                except Exception as e:
                    continue

            if best_element:
                print(f"   ✅ Found photo viewer image: {best_size} pixels area")
                return best_element
            else:
                print(f"   ⚠️ No suitable photo viewer image found")
                return None

        except Exception as e:
            print(f"❌ Error finding photo viewer image element: {e}")
            return None

    def _try_url_download(self, index, output_dir):
        """Try to download image from URL without ID validation barriers"""
        try:
            # Wait a moment for the image viewer to load
            time.sleep(3)

            # Look for the full resolution image URL in the current page (no ID validation)
            image_url = self._find_full_resolution_image_fast()

            if not image_url:
                print(f"   ❌ No image URL found for image {index + 1}")
                return None

            print(f"   🔗 Found image URL: {image_url[:80]}...")

            # Generate simple filename without asset ID dependency
            filename = f"ebird_image_{index + 1:03d}.jpg"

            # Attempt download (no duplicate checking)
            download_success = self._download_image(image_url, filename, output_dir)

            if download_success:
                filepath = os.path.join(output_dir, filename)
                print(f"   ✅ URL download successful")
                return filepath
            else:
                print(f"   ❌ URL download failed")
                return None

        except Exception as e:
            print(f"   ❌ URL download error for image {index + 1}: {e}")
            return None

    def _try_screenshot_capture(self, index, output_dir):
        """Try to capture image via screenshot"""
        try:
            # Generate filename
            filename = f"ebird_screenshot_{index + 1:03d}.png"
            filepath = os.path.join(output_dir, filename)

            # Take screenshot of current page
            print(f"   📸 Taking screenshot...")
            self.wd.save_screenshot(filepath)

            if os.path.exists(filepath):
                print(f"   ✅ Screenshot saved: {filename}")
                return filepath
            else:
                print(f"   ❌ Screenshot file not created: {filename}")
                return None

        except Exception as e:
            print(f"   ❌ Screenshot error for image {index + 1}: {e}")
            return None

    def _click_and_screenshot_image(self, clickable_img, index, output_dir):
        """Click image and take screenshot - simplified method focused on screenshots"""
        try:
            print(f"🖱️ Clicking image {index + 1} for screenshot...")

            # Click the image to open full view
            success = self._click_image_element(clickable_img, index)
            if not success:
                print(f"❌ Failed to click image {index + 1}")
                return None

            # Wait for image to load
            print(f"⏳ Waiting for full image to load...")
            time.sleep(4)

            # Take screenshot of the full image viewer
            screenshot_path = self._take_full_image_screenshot(index + 1, output_dir)

            if screenshot_path:
                print(f"📸 Screenshot taken: {screenshot_path}")

                # Return to gallery
                print(f"🔄 Returning to gallery...")
                self._return_to_main_page_with_context(index)

                return screenshot_path
            else:
                print(f"❌ Failed to take screenshot for image {index + 1}")
                return None

        except Exception as e:
            print(f"❌ Error processing image {index + 1}: {e}")
            return None

    def _click_image_element(self, clickable_img, index):
        """Click the image element to open full view"""
        try:
            # Extract WebElement from dictionary
            img_element = clickable_img['element']
            parent_link = clickable_img.get('parent_link')

            # Scroll to image
            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", img_element)
            time.sleep(1)

            # Try clicking parent link first (more reliable)
            if parent_link:
                try:
                    parent_link.click()
                    print(f"✅ Parent link clicked for image {index + 1}")
                    return True
                except Exception as parent_error:
                    print(f"⚠️ Parent link click failed: {parent_error}")

            # Try clicking the image directly
            try:
                img_element.click()
                print(f"✅ Image {index + 1} clicked successfully")
                return True

            except Exception as direct_click_error:
                print(f"⚠️ Direct click failed, trying JavaScript click...")

                # Try JavaScript click as fallback
                element_to_click = parent_link if parent_link else img_element
                self.wd.execute_script("arguments[0].click();", element_to_click)
                print(f"✅ JavaScript click successful for image {index + 1}")
                return True

        except Exception as e:
            print(f"❌ Failed to click image {index + 1}: {e}")
            return False

    def _take_full_image_screenshot(self, image_number, output_dir):
        """Take screenshot of the full image viewer"""
        try:
            # Wait a bit more for image to fully load
            time.sleep(2)

            # Generate filename
            filename = f"ebird_screenshot_{image_number:03d}.png"
            filepath = os.path.join(output_dir, filename)

            # Take screenshot
            print(f"   📸 Taking screenshot...")
            self.wd.save_screenshot(filepath)

            if os.path.exists(filepath):
                print(f"   ✅ Screenshot saved: {filename}")
                return filepath
            else:
                print(f"   ❌ Screenshot file not created: {filename}")
                return None

        except Exception as e:
            print(f"❌ Error taking screenshot: {e}")
            return None

    def _wait_for_new_image_and_get_url(self, clicked_img_src, index):
        """Wait for UNIQUE image viewer to load and get URL with duplicate detection"""
        try:
            print(f"🔍 Waiting for UNIQUE image viewer to load (image {index + 1})...")

            max_wait_attempts = 15  # 15 seconds max wait
            wait_interval = 1
            last_detected_asset_id = None

            for attempt in range(max_wait_attempts):
                time.sleep(wait_interval)

                # Check if we're now in a modal/viewer state
                if self._is_in_image_viewer():
                    print(f"✅ Image viewer detected (attempt {attempt + 1})")

                    # Fast asset ID detection
                    current_asset_id = self._get_current_image_asset_id()

                    if current_asset_id:
                        # Check if this is a unique asset ID
                        if self._is_asset_id_unique(current_asset_id):
                            print(f"✅ UNIQUE asset ID found: {current_asset_id}")

                            # Get the full resolution URL
                            full_image_url = self._find_full_resolution_image_fast()

                            if full_image_url:
                                # Verify the URL contains the same asset ID
                                url_asset_id = self._extract_asset_id_fast(full_image_url)
                                if url_asset_id == current_asset_id:
                                    print(f"✅ URL verified with asset ID: {current_asset_id}")
                                    return full_image_url
                                else:
                                    print(f"⚠️ URL asset ID mismatch: {url_asset_id} vs {current_asset_id}")
                            else:
                                print(f"⚠️ Could not get full resolution URL for asset {current_asset_id}")
                        else:
                            print(f"⚠️ Duplicate asset ID detected: {current_asset_id} (attempt {attempt + 1})")
                            last_detected_asset_id = current_asset_id

                            # If we keep getting the same duplicate, try to refresh
                            if attempt > 5:
                                print(f"🔄 Same duplicate detected multiple times, forcing refresh...")
                                self._clear_browser_cache_and_refresh()
                                time.sleep(2)
                    else:
                        print(f"⏳ No asset ID detected yet (attempt {attempt + 1})")
                else:
                    print(f"⏳ Waiting for image viewer to load... (attempt {attempt + 1})")

                # Enhanced refresh every 5 attempts
                if attempt > 0 and attempt % 5 == 0:
                    print(f"🔄 Enhanced refresh (attempt {attempt + 1})")
                    self._clear_browser_cache_and_refresh()
                    time.sleep(2)

            # Final check - if we detected a duplicate consistently, report it
            if last_detected_asset_id:
                print(f"❌ Timeout: Only duplicate asset ID {last_detected_asset_id} detected")
                self._current_session_stats['failed_detections'] += 1
            else:
                print(f"❌ Timeout: No asset ID detected at all")
                self._current_session_stats['failed_detections'] += 1

            return None

        except Exception as e:
            print(f"❌ Error waiting for unique image: {e}")
            self._current_session_stats['failed_detections'] += 1
            return None

    def _is_different_image(self, full_url, thumbnail_src):
        """Check if the full resolution URL is different from the thumbnail source"""
        try:
            if not full_url or not thumbnail_src:
                return True  # If we can't compare, assume it's different

            # Extract asset ID from URLs for comparison
            import re

            # Extract asset ID from full URL
            full_match = re.search(r'/asset/(\d+)/', full_url)
            full_asset_id = full_match.group(1) if full_match else None

            # Extract asset ID from thumbnail URL
            thumb_match = re.search(r'/asset/(\d+)/', thumbnail_src)
            thumb_asset_id = thumb_match.group(1) if thumb_match else None

            if full_asset_id and thumb_asset_id:
                is_different = full_asset_id != thumb_asset_id
                if is_different:
                    print(f"✅ Confirmed different image: {full_asset_id} vs {thumb_asset_id}")
                else:
                    print(f"⚠️ Same image detected: {full_asset_id} = {thumb_asset_id}")
                return is_different

            # Fallback: compare URLs directly
            return full_url != thumbnail_src

        except Exception as e:
            print(f"⚠️ Error comparing images, assuming different: {e}")
            return True

    def _is_in_image_viewer(self):
        """Check if we're currently in an image viewer/modal state (not in gallery view)"""
        try:
            # Check for common modal/viewer indicators
            viewer_selectors = [
                # Modal/lightbox containers
                ".modal:not([style*='display: none'])",
                ".lightbox:not([style*='display: none'])",
                ".fullscreen:not([style*='display: none'])",

                # eBird specific viewers
                ".MediaDisplay",
                ".MediaViewer",
                ".FullscreenImage",
                "[data-testid='media-display']",

                # Generic overlay/popup indicators
                ".overlay:not([style*='display: none'])",
                ".popup:not([style*='display: none'])",
                "[role='dialog']:not([style*='display: none'])",

                # Check for backdrop/overlay elements
                ".modal-backdrop",
                ".overlay-backdrop",

                # Look for close buttons (indicates modal is open)
                "button[aria-label*='close']:not([style*='display: none'])",
                "button[title*='close']:not([style*='display: none'])",
                ".close-button:not([style*='display: none'])"
            ]

            for selector in viewer_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            print(f"   ✅ Image viewer detected: {selector}")
                            return True
                except:
                    continue

            # Additional check: look for URL changes that indicate viewer mode
            current_url = self.wd.current_url
            if any(indicator in current_url.lower() for indicator in ['modal', 'viewer', 'fullscreen', 'asset']):
                print(f"   ✅ Image viewer detected via URL: {current_url}")
                return True

            # Check if page title changed (some sites change title in viewer mode)
            try:
                current_title = self.wd.title
                if any(indicator in current_title.lower() for indicator in ['photo', 'image', 'media']):
                    print(f"   ✅ Image viewer detected via title: {current_title}")
                    return True
            except:
                pass

            return False

        except Exception as e:
            print(f"❌ Error checking image viewer state: {e}")
            return False  # If error, assume we're not in viewer to avoid infinite loop

    def _clear_browser_cache_and_refresh(self):
        """Clear browser cache and refresh page state to prevent stale content detection"""
        try:
            print("   🧹 Clearing browser cache and refreshing...")

            # Method 1: Clear browser cache via DevTools (most effective)
            try:
                self.wd.execute_cdp_cmd('Network.clearBrowserCache', {})
                print("   ✅ Browser cache cleared via DevTools")
            except Exception as e:
                print(f"   ⚠️ DevTools cache clear failed: {e}")

            # Method 2: Clear browser cookies and local storage
            try:
                self.wd.delete_all_cookies()
                self.wd.execute_script("window.localStorage.clear();")
                self.wd.execute_script("window.sessionStorage.clear();")
                print("   ✅ Cookies and storage cleared")
            except Exception as e:
                print(f"   ⚠️ Storage clear failed: {e}")

            # Method 3: Force hard refresh
            try:
                self.wd.execute_script("location.reload(true);")  # Hard refresh
                time.sleep(2)
                print("   ✅ Hard refresh completed")
            except Exception as e:
                print(f"   ⚠️ Hard refresh failed: {e}")

            # Method 4: Additional DOM manipulation to force re-render
            try:
                self.wd.execute_script("""
                    // Force DOM re-render
                    document.body.style.display = 'none';
                    document.body.offsetHeight; // Trigger reflow
                    document.body.style.display = '';

                    // Force image cache invalidation
                    var images = document.querySelectorAll('img');
                    images.forEach(function(img) {
                        if (img.src) {
                            var src = img.src;
                            img.src = '';
                            img.src = src + (src.includes('?') ? '&' : '?') + '_t=' + Date.now();
                        }
                    });
                """)
                print("   ✅ DOM refresh and image cache invalidation completed")
            except Exception as e:
                print(f"   ⚠️ DOM refresh failed: {e}")

        except Exception as e:
            print(f"   ❌ Error during cache clearing: {e}")

    def _progressive_wait_for_viewer(self, index):
        """Progressive wait strategy: start fast, increase timeout if needed"""
        try:
            print(f"   🎯 Using progressive wait strategy for image {index + 1}...")

            # Phase 1: Quick check (0.5s intervals, 3 attempts = 1.5s total)
            print(f"   ⚡ Phase 1: Quick detection...")
            for attempt in range(3):
                time.sleep(0.5)
                if self._is_in_image_viewer():
                    print(f"   ✅ Quick detection successful (0.{5 * (attempt + 1)}s)")
                    return True

            # Phase 2: Medium wait (1s intervals, 4 attempts = 4s total)
            print(f"   ⏳ Phase 2: Medium wait...")
            for attempt in range(4):
                time.sleep(1)
                if self._is_in_image_viewer():
                    print(f"   ✅ Medium wait successful ({1.5 + attempt + 1}s)")
                    return True

            # Phase 3: Longer wait with DOM checks (2s intervals, 3 attempts = 6s total)
            print(f"   🔍 Phase 3: Deep detection with DOM analysis...")
            for attempt in range(3):
                time.sleep(2)

                # Force DOM update
                self.wd.execute_script("document.body.offsetHeight;")  # Trigger reflow

                if self._is_in_image_viewer():
                    print(f"   ✅ Deep detection successful ({5.5 + (attempt + 1) * 2}s)")
                    return True

                # Additional check: look for any significant DOM changes
                if self._detect_dom_changes():
                    print(f"   ✅ DOM changes detected, assuming viewer loaded")
                    return True

            print(f"   ❌ Progressive wait failed after ~11.5 seconds")
            return False

        except Exception as e:
            print(f"   ❌ Error in progressive wait: {e}")
            return False

    def _detect_dom_changes(self):
        """Detect if significant DOM changes occurred (indicating page state change)"""
        try:
            # Check for common indicators of page state changes
            indicators = [
                # Check if body class changed (many sites add modal classes)
                "document.body.className.includes('modal') || document.body.className.includes('overlay')",

                # Check for z-index changes (modals usually have high z-index)
                "Array.from(document.querySelectorAll('*')).some(el => window.getComputedStyle(el).zIndex > 1000)",

                # Check for backdrop elements
                "document.querySelector('.modal-backdrop, .overlay-backdrop, [role=\"dialog\"]') !== null",

                # Check for elements with high opacity that weren't there before
                "Array.from(document.querySelectorAll('*')).some(el => el.style.position === 'fixed' && window.getComputedStyle(el).zIndex > 100)"
            ]

            for indicator in indicators:
                try:
                    result = self.wd.execute_script(f"return {indicator};")
                    if result:
                        print(f"   ✅ DOM change detected: {indicator[:50]}...")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"   ❌ Error detecting DOM changes: {e}")
            return False

    def _extract_asset_id_fast(self, url):
        """Fast asset ID extraction from URL"""
        try:
            if not url:
                return None

            import re
            # Extract asset ID from URL patterns
            patterns = [
                r'/asset/(\d+)/',
                r'asset_id=(\d+)',
                r'assetId=(\d+)',
                r'/(\d{8,})',  # 8+ digit numbers (common for asset IDs)
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    asset_id = match.group(1)
                    return asset_id

            return None

        except Exception as e:
            print(f"   ❌ Error extracting asset ID: {e}")
            return None

    def _is_asset_id_unique(self, asset_id):
        """Check if asset ID is unique (not already downloaded)"""
        if not asset_id:
            return False

        is_unique = asset_id not in self._downloaded_asset_ids

        if not is_unique:
            print(f"   ⚠️ Duplicate asset ID detected: {asset_id}")
            self._current_session_stats['duplicates_skipped'] += 1

        return is_unique

    def _mark_asset_as_downloaded(self, asset_id):
        """Mark asset ID as downloaded"""
        if asset_id:
            self._downloaded_asset_ids.add(asset_id)
            self._current_session_stats['unique_downloads'] += 1
            print(f"   ✅ Asset ID {asset_id} marked as downloaded")

    def _get_current_image_asset_id(self):
        """Get asset ID of currently displayed image in viewer - FAST method"""
        try:
            print(f"   🔍 Fast asset ID detection...")

            # Method 1: Direct URL inspection from current page
            current_url = self.wd.current_url
            asset_id = self._extract_asset_id_fast(current_url)
            if asset_id:
                print(f"   ✅ Asset ID from URL: {asset_id}")
                return asset_id

            # Method 2: Fast DOM inspection for image URLs
            fast_selectors = [
                # High priority - main image in viewer
                ".MediaDisplay img[src*='macaulaylibrary.org']",
                ".MediaViewer img[src*='macaulaylibrary.org']",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # Fallback - any large visible image
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])"
            ]

            for selector in fast_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            img_src = element.get_attribute('src')
                            if img_src:
                                asset_id = self._extract_asset_id_fast(img_src)
                                if asset_id:
                                    print(f"   ✅ Asset ID from DOM: {asset_id}")
                                    return asset_id
                except:
                    continue

            # Method 3: JavaScript execution for active image
            try:
                js_script = """
                // Find the most prominent image
                var images = document.querySelectorAll('img[src*="macaulaylibrary.org"]');
                var largestImg = null;
                var maxArea = 0;

                for (var i = 0; i < images.length; i++) {
                    var img = images[i];
                    if (img.offsetWidth > 0 && img.offsetHeight > 0) {
                        var area = img.offsetWidth * img.offsetHeight;
                        if (area > maxArea) {
                            maxArea = area;
                            largestImg = img;
                        }
                    }
                }

                return largestImg ? largestImg.src : null;
                """

                img_src = self.wd.execute_script(js_script)
                if img_src:
                    asset_id = self._extract_asset_id_fast(img_src)
                    if asset_id:
                        print(f"   ✅ Asset ID from JavaScript: {asset_id}")
                        return asset_id
            except Exception as js_error:
                print(f"   ⚠️ JavaScript asset detection failed: {js_error}")

            print(f"   ❌ No asset ID found")
            return None

        except Exception as e:
            print(f"   ❌ Error getting current image asset ID: {e}")
            return None

    def _find_full_resolution_image_fast(self):
        """Fast method to find full resolution image URL - optimized for speed"""
        try:
            print(f"🚀 Fast full resolution image detection...")

            # Method 1: Direct high-priority selectors (fastest)
            priority_selectors = [
                # eBird specific high-res patterns
                ".MediaDisplay img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                ".MediaViewer img[src*='macaulaylibrary.org']:not([src*='small'])",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # High resolution indicators
                "img[src*='macaulaylibrary.org'][src*='original']",
                "img[src*='macaulaylibrary.org'][src*='full']",
                "img[src*='macaulaylibrary.org'][src*='2400']",
                "img[src*='macaulaylibrary.org'][src*='1920']"
            ]

            for selector in priority_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            img_url = element.get_attribute('src')
                            if img_url and self._is_high_quality_url(img_url):
                                print(f"✅ Fast detection successful: {img_url[:80]}...")
                                return self._convert_to_ultra_high_res(img_url)
                except:
                    continue

            # Method 2: JavaScript-based detection (medium speed)
            try:
                js_script = """
                // Find the largest, most prominent image
                var images = document.querySelectorAll('img[src*="macaulaylibrary.org"]');
                var bestImg = null;
                var bestScore = 0;

                for (var i = 0; i < images.length; i++) {
                    var img = images[i];
                    if (img.offsetWidth > 0 && img.offsetHeight > 0 && img.src) {
                        var score = 0;

                        // Size score
                        score += img.offsetWidth * img.offsetHeight / 1000;

                        // Quality indicators
                        if (img.src.includes('original')) score += 1000;
                        if (img.src.includes('full')) score += 800;
                        if (img.src.includes('2400') || img.src.includes('1920')) score += 600;

                        // Penalties
                        if (img.src.includes('thumbnail') || img.src.includes('small')) score -= 500;

                        if (score > bestScore) {
                            bestScore = score;
                            bestImg = img;
                        }
                    }
                }

                return bestImg ? bestImg.src : null;
                """

                best_url = self.wd.execute_script(js_script)
                if best_url and self._is_high_quality_url(best_url):
                    print(f"✅ JavaScript detection successful: {best_url[:80]}...")
                    return self._convert_to_ultra_high_res(best_url)

            except Exception as js_error:
                print(f"   ⚠️ JavaScript detection failed: {js_error}")

            # Method 3: Fallback to any visible eBird image
            try:
                fallback_elements = self.wd.find_elements(By.CSS_SELECTOR, "img[src*='macaulaylibrary.org']")
                for element in fallback_elements:
                    if element.is_displayed():
                        img_url = element.get_attribute('src')
                        if img_url:
                            print(f"⚠️ Fallback detection: {img_url[:80]}...")
                            return self._convert_to_ultra_high_res(img_url)
            except:
                pass

            print(f"❌ No full resolution image found")
            return None

        except Exception as e:
            print(f"❌ Error in fast full resolution detection: {e}")
            return None

    def _is_high_quality_url(self, url):
        """Quick check if URL indicates high quality image"""
        if not url:
            return False

        url_lower = url.lower()

        # High quality indicators
        quality_indicators = ['original', 'full', '2400', '1920', 'xlarge', 'large']
        has_quality = any(indicator in url_lower for indicator in quality_indicators)

        # Low quality penalties
        low_quality = any(indicator in url_lower for indicator in ['thumbnail', 'thumb', 'small', 'tiny'])

        return has_quality or not low_quality

    def _print_asset_tracking_stats(self):
        """Print comprehensive asset ID tracking statistics"""
        try:
            print("\n" + "="*60)
            print("📊 ASSET ID TRACKING STATISTICS")
            print("="*60)

            stats = self._current_session_stats
            print(f"🎯 Images Attempted: {stats['attempted']}")
            print(f"✅ Unique Downloads: {stats['unique_downloads']}")
            print(f"⚠️ Duplicates Skipped: {stats['duplicates_skipped']}")
            print(f"❌ Failed Detections: {stats['failed_detections']}")

            if stats['attempted'] > 0:
                success_rate = (stats['unique_downloads'] / stats['attempted']) * 100
                duplicate_rate = (stats['duplicates_skipped'] / stats['attempted']) * 100
                print(f"📈 Success Rate: {success_rate:.1f}%")
                print(f"📉 Duplicate Rate: {duplicate_rate:.1f}%")

            print(f"🗂️ Total Unique Assets Downloaded: {len(self._downloaded_asset_ids)}")

            if self._downloaded_asset_ids:
                print(f"📋 Downloaded Asset IDs:")
                for i, asset_id in enumerate(sorted(self._downloaded_asset_ids), 1):
                    print(f"   {i:2d}. {asset_id}")

            print("="*60)

        except Exception as e:
            print(f"❌ Error printing asset tracking stats: {e}")

    def _reset_session_stats(self):
        """Reset session statistics (keep downloaded asset IDs)"""
        self._current_session_stats = {
            'attempted': 0,
            'unique_downloads': 0,
            'duplicates_skipped': 0,
            'failed_detections': 0
        }
        print("🔄 Session statistics reset")

    def _clear_all_tracking(self):
        """Clear all tracking data (use with caution)"""
        self._downloaded_asset_ids.clear()
        self._reset_session_stats()
        print("🧹 All asset tracking data cleared")

    def _get_duplicate_prevention_summary(self):
        """Get summary of duplicate prevention effectiveness"""
        stats = self._current_session_stats
        total_processed = stats['unique_downloads'] + stats['duplicates_skipped']

        if total_processed == 0:
            return "No images processed yet"

        prevention_rate = (stats['duplicates_skipped'] / total_processed) * 100

        summary = f"Duplicate Prevention: {stats['duplicates_skipped']} duplicates prevented out of {total_processed} total detections ({prevention_rate:.1f}% prevention rate)"

        return summary

    def _handle_processing_error(self, image_index, error, stats):
        """Comprehensive error handling with circuit breaker pattern"""
        try:
            print(f"🚨 Handling processing error for image {image_index + 1}: {error}")

            # Initialize error tracking if not exists
            if not hasattr(self, '_error_tracker'):
                self._error_tracker = {
                    'consecutive_failures': 0,
                    'total_failures': 0,
                    'last_error_time': 0,
                    'error_types': {}
                }

            # Update error tracking
            self._error_tracker['consecutive_failures'] += 1
            self._error_tracker['total_failures'] += 1
            self._error_tracker['last_error_time'] = time.time()

            error_type = type(error).__name__
            self._error_tracker['error_types'][error_type] = self._error_tracker['error_types'].get(error_type, 0) + 1

            # Circuit breaker logic
            if self._error_tracker['consecutive_failures'] >= 3:
                print(f"🔴 Circuit breaker triggered: {self._error_tracker['consecutive_failures']} consecutive failures")

                # Check if we should stop processing
                if self._error_tracker['consecutive_failures'] >= 5:
                    print(f"❌ Too many consecutive failures ({self._error_tracker['consecutive_failures']}), stopping to prevent infinite loops")
                    return False

                # Enhanced recovery for circuit breaker state
                print(f"🔧 Attempting enhanced recovery...")
                recovery_success = self._enhanced_error_recovery()

                if recovery_success:
                    print(f"✅ Enhanced recovery successful, resetting circuit breaker")
                    self._error_tracker['consecutive_failures'] = 0
                    return True
                else:
                    print(f"❌ Enhanced recovery failed")
                    return False

            # Standard recovery for minor errors
            print(f"🔧 Attempting standard recovery...")
            recovery_success = self._standard_error_recovery()

            if recovery_success:
                print(f"✅ Standard recovery successful")
                return True
            else:
                print(f"⚠️ Standard recovery failed, error count: {self._error_tracker['consecutive_failures']}")
                return True  # Continue but with increased error count

        except Exception as recovery_error:
            print(f"❌ Error in error handling: {recovery_error}")
            return False

    def _enhanced_error_recovery(self):
        """Enhanced recovery for critical error states"""
        try:
            print(f"   🔧 Enhanced recovery: Full browser state reset...")

            # Step 1: Clear all browser state
            self._clear_browser_cache_and_refresh()
            time.sleep(3)

            # Step 2: Navigate back to original gallery
            if hasattr(self, '_original_gallery_url') and self._original_gallery_url:
                print(f"   🔄 Navigating back to original gallery...")
                self.wd.get(self._original_gallery_url)
                time.sleep(5)

            # Step 3: Wait for gallery to be ready
            if self._wait_for_gallery_ready(timeout=30):
                print(f"   ✅ Gallery ready after enhanced recovery")
                return True
            else:
                print(f"   ❌ Gallery not ready after enhanced recovery")
                return False

        except Exception as e:
            print(f"   ❌ Enhanced recovery failed: {e}")
            return False

    def _standard_error_recovery(self):
        """Standard recovery for minor errors"""
        try:
            print(f"   🔧 Standard recovery: Page state reset...")

            # Step 1: Try to return to gallery
            recovery_success = self._recover_page_state()
            if not recovery_success:
                print(f"   ⚠️ Page state recovery failed")
                return False

            # Step 2: Verify gallery is ready
            if self._verify_ready_for_next_image():
                print(f"   ✅ Gallery ready after standard recovery")
                return True
            else:
                print(f"   ⚠️ Gallery not ready after standard recovery")
                return False

        except Exception as e:
            print(f"   ❌ Standard recovery failed: {e}")
            return False

    def _return_to_main_page_with_context(self, image_index):
        """Context-aware navigation back to main page with specific handling for image processing"""
        print(f"🔄 Returning to main gallery after processing image {image_index + 1}...")

        try:
            # Store original URL before any navigation attempts
            original_url = getattr(self, '_original_gallery_url', None)

            # Step 1: Close any modals/overlays first
            print("   🔄 Closing modals/overlays...")
            self._close_modal_or_overlay()
            time.sleep(2)

            # Step 2: Check if we're already on the main page
            current_url = self.wd.current_url
            if self._is_gallery_page(current_url):
                print("   ✅ Already on gallery page")
                if self._verify_ready_for_next_image():
                    return True

            # Step 3: Try browser back button first (most reliable)
            print("   ⬅️ Using browser back button...")
            try:
                self.wd.back()
                time.sleep(3)

                # Check if back button worked
                if self._verify_ready_for_next_image():
                    print(f"✅ Successfully returned via back button")
                    return True
            except Exception as e:
                print(f"   ⚠️ Back button failed: {e}")

            # Step 4: If we have original URL, navigate directly
            if original_url:
                print(f"   🌐 Navigating to original URL: {original_url}")
                try:
                    self.wd.get(original_url)
                    time.sleep(5)

                    if self._verify_ready_for_next_image():
                        print(f"✅ Successfully returned via direct navigation")
                        return True
                except Exception as e:
                    print(f"   ⚠️ Direct navigation failed: {e}")

            # Step 5: Try page refresh as last resort
            print("   🔄 Trying page refresh...")
            try:
                self.wd.refresh()
                time.sleep(5)

                if self._verify_ready_for_next_image():
                    print(f"✅ Successfully recovered via refresh")
                    return True
            except Exception as e:
                print(f"   ⚠️ Refresh failed: {e}")

            print(f"❌ All navigation attempts failed for image {image_index + 1}")
            return False

        except Exception as e:
            print(f"❌ Error in context-aware navigation for image {image_index + 1}: {e}")
            return False

    def _is_gallery_page(self, url):
        """Check if the current URL is a gallery page"""
        try:
            gallery_indicators = [
                'catalog',
                'media',
                'ebird.org',
                'gallery'
            ]

            url_lower = url.lower()
            return any(indicator in url_lower for indicator in gallery_indicators)
        except:
            return False

    def _store_original_gallery_url(self):
        """Store the original gallery URL for navigation purposes"""
        try:
            current_url = self.wd.current_url
            if self._is_gallery_page(current_url):
                self._original_gallery_url = current_url
                print(f"📌 Stored original gallery URL: {current_url}")
        except Exception as e:
            print(f"⚠️ Could not store original URL: {e}")

    def _verify_ready_for_next_image(self):
        """Verify that the page is ready for processing the next image"""
        try:
            # Check that we have clickable images available
            clickable_images = self._get_clickable_images_from_page()

            if len(clickable_images) > 0:
                print(f"   ✅ Found {len(clickable_images)} clickable images, ready for next")
                return True
            else:
                print(f"   ⚠️ No clickable images found, page may not be ready")
                return False

        except Exception as e:
            print(f"   ❌ Error verifying readiness for next image: {e}")
            return False

    def _recover_page_state(self):
        """Recover page state when navigation or page readiness fails"""
        print("🔧 Attempting to recover page state...")

        try:
            # Strategy 1: Refresh the page
            print("   🔄 Refreshing page...")
            self.wd.refresh()
            time.sleep(5)

            # Wait for page to load
            if self._wait_for_gallery_ready(timeout=15):
                print("   ✅ Page recovered successfully via refresh")
                return True

            # Strategy 2: Navigate back to original URL
            print("   🌐 Attempting to navigate back to original URL...")
            current_url = self.wd.current_url
            if 'catalog' in current_url and 'ebird.org' in current_url:
                self.wd.get(current_url)
                time.sleep(5)

                if self._wait_for_gallery_ready(timeout=15):
                    print("   ✅ Page recovered successfully via navigation")
                    return True

            # Strategy 3: Try browser back button
            print("   ⬅️ Trying browser back button...")
            self.wd.back()
            time.sleep(3)

            if self._wait_for_gallery_ready(timeout=10):
                print("   ✅ Page recovered successfully via back button")
                return True

            print("   ❌ All recovery strategies failed")
            return False

        except Exception as e:
            print(f"   ❌ Error during page recovery: {e}")
            return False

    def _find_full_resolution_image(self):
        """Enhanced detection untuk gambar resolusi penuh - mencari URL terbaik"""
        try:
            print(f"🔍 Searching for FULL RESOLUTION image URL...")

            # Wait for image to fully load
            time.sleep(3)

            # Ultra high-priority selectors untuk URL resolusi maksimal
            ultra_high_res_selectors = [
                # Modal dan viewer images (prioritas tertinggi)
                ".modal img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",
                ".lightbox img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",
                ".fullscreen img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",

                # eBird specific viewers
                ".MediaDisplay img[src*='macaulaylibrary.org']",
                ".MediaViewer img[src*='macaulaylibrary.org']",
                ".FullscreenImage img[src*='macaulaylibrary.org']",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # Images dengan indikator resolusi tinggi
                "img[src*='macaulaylibrary.org'][src*='original']",
                "img[src*='macaulaylibrary.org'][src*='full']",
                "img[src*='macaulaylibrary.org'][src*='xlarge']",
                "img[src*='macaulaylibrary.org'][src*='2400']",
                "img[src*='macaulaylibrary.org'][src*='1920']",

                # Cornell CDN high-res
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",

                # General high-quality indicators
                "img[style*='max-width'][src*='macaulaylibrary.org']",
                "img[alt*='photo'][src*='macaulaylibrary.org']",

                # Fallback ke semua eBird images
                "img[src*='macaulaylibrary.org']"
            ]

            best_url = None
            best_score = 0

            for selector in ultra_high_res_selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        if img.is_displayed():
                            img_url = img.get_attribute('src')

                            if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                                # Calculate URL quality score
                                score = self._calculate_url_quality_score(img_url, img)

                                if score > best_score:
                                    best_url = img_url
                                    best_score = score
                                    print(f"🎯 Found better URL (score: {score}): {img_url[:80]}...")

                except Exception as e:
                    continue

            if best_url:
                # Convert to absolute highest resolution
                ultra_high_res_url = self._convert_to_ultra_high_res(best_url)
                print(f"✅ BEST RESOLUTION URL found (score: {best_score})")
                return ultra_high_res_url
            else:
                print(f"⚠️ No high-resolution URL found")
                return None

        except Exception as e:
            print(f"❌ Error finding full resolution image: {e}")
            return None

    def _calculate_url_quality_score(self, url, element):
        """Calculate quality score for an image URL"""
        try:
            score = 0
            url_lower = url.lower()

            # URL quality indicators (higher score = better quality)
            if 'original' in url_lower:
                score += 1000
            elif 'full' in url_lower:
                score += 800
            elif 'xlarge' in url_lower:
                score += 600
            elif 'large' in url_lower:
                score += 400
            elif '2400' in url or '1920' in url:
                score += 500

            # Penalties for low quality indicators
            if 'thumbnail' in url_lower or 'thumb' in url_lower:
                score -= 800
            elif 'small' in url_lower:
                score -= 400
            elif 'medium' in url_lower:
                score -= 200

            # Element size bonus
            try:
                size = element.size
                area = size['width'] * size['height']
                score += area / 1000  # Normalize area to score
            except:
                pass

            # Context bonus (modal/viewer context is better)
            try:
                parent_classes = element.find_element(By.XPATH, './..').get_attribute('class') or ''
                if any(cls in parent_classes.lower() for cls in ['modal', 'viewer', 'fullscreen', 'lightbox']):
                    score += 300
            except:
                pass

            return score

        except:
            return 0

    def _convert_to_ultra_high_res(self, img_url):
        """Convert URL to absolute maximum resolution"""
        if not img_url:
            return None

        try:
            print(f"🚀 Converting to ULTRA HIGH RESOLUTION...")
            original_url = img_url

            if 'macaulaylibrary.org' in img_url:
                # Remove all size parameters
                base_url = img_url.split('?')[0]

                # Try the absolute best resolution parameters
                ultra_params = [
                    '?size=original&quality=100',
                    '?width=5000&height=4000&quality=100', 
                    '?size=full&quality=100',
                    '?size=xlarge&quality=100',
                    '?width=4000&height=3000&quality=100', 
                    '?size=4K&quality=100',   
                    '?w=2400&h=1600&q=100',
                    '?size=2400&quality=100',
                    '?original=true',
                    ''  # Sometimes no parameters gives original
                ]

                # Test each parameter and pick the best
                for param in ultra_params:
                    test_url = base_url + param
                    if self._verify_high_quality_url(test_url):
                        print(f"✅ Ultra high-res URL verified: {param}")
                        return test_url

                # If no verification possible, use the best parameter
                return base_url + '?size=original&quality=100'

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Cornell CDN optimization
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')
                img_url = img_url.replace('_large', '')

                # Add quality parameters
                if '?' not in img_url:
                    img_url += '?quality=100&size=original'

                return img_url

            return img_url

        except Exception as e:
            print(f"❌ Error converting to ultra high-res: {e}")
            return original_url

    def _verify_high_quality_url(self, url):
        """Verify if URL returns high quality image"""
        try:
            import requests
            response = requests.head(url, timeout=3)
            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length:
                    size_kb = int(content_length) / 1024
                    # High quality images should be at least 100KB
                    return size_kb > 100
        except:
            pass
        return False

    def _take_screenshot(self, index, output_dir=None, crop_to_bird=True):
        """Ultra high-quality screenshot functionality - mimics manual screenshot behavior"""
        try:
            print(f"📸 Taking ULTRA HIGH-QUALITY screenshot for image {index + 1}...")

            # Set output directory
            if output_dir is None:
                output_dir = os.getcwd()

            # Wait for full loading and animations
            time.sleep(3)

            # Set browser to maximum quality mode
            self._optimize_browser_for_screenshots()

            # Hide all UI overlays and distractions
            self._hide_ui_overlays()

            # Try to maximize the image display first
            self._maximize_image_display()

            # Find the absolute best image element
            screenshot_element = self._find_ultra_high_res_target()

            if screenshot_element:
                # Get element dimensions and optimize
                element_size = screenshot_element.size
                print(f"🎯 Target element size: {element_size['width']}x{element_size['height']} pixels")

                # Scroll and center perfectly
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });

                    // Remove any transforms or filters that might reduce quality
                    arguments[0].style.transform = 'none';
                    arguments[0].style.filter = 'none';
                    arguments[0].style.opacity = '1';

                    // Ensure maximum quality display
                    arguments[0].style.imageRendering = 'high-quality';
                    arguments[0].style.imageRendering = '-webkit-optimize-contrast';
                """, screenshot_element)

                time.sleep(2)  # Wait for rendering

                # Take ultra high-quality screenshot
                screenshot_filename = f"ebird_ultra_hq_{index + 1:03d}.png"
                screenshot_path = os.path.join(output_dir, screenshot_filename)

                # Try multiple screenshot methods for best quality
                success = self._take_ultra_quality_screenshot(screenshot_element, screenshot_path, crop_to_bird=False)

                if success:
                    file_size = os.path.getsize(screenshot_path)
                    print(f"✅ ULTRA HIGH-QUALITY screenshot saved: {screenshot_filename}")
                    print(f"📊 File size: {file_size/1024:.1f} KB")
                    return screenshot_path
                else:
                    print("🔄 Ultra quality failed, trying enhanced fallback...")
                    return self._take_enhanced_fallback_screenshot(index, output_dir)

            else:
                print("🔄 No ultra-high-res target found, trying enhanced fallback...")
                return self._take_enhanced_fallback_screenshot(index, output_dir)

        except Exception as e:
            print(f"❌ Error in ultra high-quality screenshot: {e}")
            return self._take_enhanced_fallback_screenshot(index, output_dir)

    def _optimize_browser_for_screenshots(self):
        """Optimize browser settings for maximum screenshot quality"""
        try:
            # Set maximum zoom for better quality
            self.wd.execute_script("document.body.style.zoom = '1.0';")

            # Disable any image compression or optimization
            self.wd.execute_script("""
                // Disable image optimization
                var style = document.createElement('style');
                style.textContent = `
                    img {
                        image-rendering: -webkit-optimize-contrast !important;
                        image-rendering: high-quality !important;
                        image-rendering: crisp-edges !important;
                        -ms-interpolation-mode: bicubic !important;
                    }
                `;
                document.head.appendChild(style);
            """)

            # Wait for styles to apply
            time.sleep(1)

        except Exception as e:
            print(f"⚠️ Warning: Could not optimize browser for screenshots: {e}")

    def _maximize_image_display(self):
        """Try to maximize the image display like manual viewing"""
        try:
            # Look for fullscreen or maximize buttons
            maximize_selectors = [
                'button[aria-label*="fullscreen"]',
                'button[aria-label*="maximize"]',
                'button[title*="fullscreen"]',
                'button[title*="maximize"]',
                '.fullscreen-button',
                '.maximize-button',
                '[data-testid*="fullscreen"]',
                '[data-testid*="maximize"]'
            ]

            for selector in maximize_selectors:
                try:
                    button = self.wd.find_element(By.CSS_SELECTOR, selector)
                    if button.is_displayed() and button.is_enabled():
                        print(f"🔍 Found maximize button, clicking...")
                        button.click()
                        time.sleep(2)
                        return True
                except:
                    continue

            # Try keyboard shortcut for fullscreen
            try:
                from selenium.webdriver.common.keys import Keys
                body = self.wd.find_element(By.TAG_NAME, 'body')
                body.send_keys(Keys.F11)  # Try F11 for fullscreen
                time.sleep(1)
            except:
                pass

        except Exception as e:
            print(f"⚠️ Could not maximize image display: {e}")

        return False

    def _find_ultra_high_res_target(self):
        """Find the absolute best, highest resolution image element"""
        try:
            # Ultra high priority selectors for maximum quality
            ultra_selectors = [
                # Full-size modal images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',
                '.lightbox img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',
                '.fullscreen img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',

                # eBird specific high-res viewers
                '.MediaDisplay img[src*="macaulaylibrary.org"]',
                '.MediaViewer img[src*="macaulaylibrary.org"]',
                '.FullscreenImage img[src*="macaulaylibrary.org"]',
                '[data-testid="media-display"] img[src*="macaulaylibrary.org"]',

                # Large display images with size indicators
                'img[src*="macaulaylibrary.org"][src*="original"]',
                'img[src*="macaulaylibrary.org"][src*="full"]',
                'img[src*="macaulaylibrary.org"][src*="xlarge"]',
                'img[src*="macaulaylibrary.org"][src*="2400"]',
                'img[src*="macaulaylibrary.org"][src*="1920"]',

                # Cornell CDN high-res images
                'img[src*="cdn.download.ams.birds.cornell.edu"]:not([src*="thumbnail"])',

                # Any large eBird image
                'img[src*="macaulaylibrary.org"]'
            ]

            best_element = None
            best_score = 0

            for selector in ultra_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate quality score
                            score = self._calculate_image_quality_score(element)

                            if score > best_score:
                                best_element = element
                                best_score = score

                except Exception as e:
                    continue

            if best_element:
                print(f"🎯 Found ULTRA HIGH-RES target with quality score: {best_score}")
                return best_element
            else:
                print("⚠️ No ultra high-res target found")
                return None

        except Exception as e:
            print(f"❌ Error finding ultra high-res target: {e}")
            return None

    def _calculate_image_quality_score(self, element):
        """Calculate quality score for an image element"""
        try:
            score = 0

            # Size score (larger is better)
            size = element.size
            area = size['width'] * size['height']
            score += area / 1000  # Normalize

            # URL quality indicators
            src = element.get_attribute('src') or ''
            if 'original' in src.lower():
                score += 1000
            elif 'full' in src.lower():
                score += 800
            elif 'xlarge' in src.lower():
                score += 600
            elif 'large' in src.lower():
                score += 400
            elif '2400' in src or '1920' in src:
                score += 500

            # Penalty for thumbnails
            if 'thumbnail' in src.lower() or 'thumb' in src.lower():
                score -= 500
            if 'small' in src.lower():
                score -= 300

            # Bonus for modal/viewer context
            parent_classes = element.find_element(By.XPATH, './..').get_attribute('class') or ''
            if any(cls in parent_classes.lower() for cls in ['modal', 'viewer', 'fullscreen', 'lightbox']):
                score += 200

            return score

        except:
            return 0

    def _take_ultra_quality_screenshot(self, element, screenshot_path, crop_to_bird=True):
        """Take the highest quality screenshot with improved reliability"""
        try:
            print(f"   📸 Attempting ultra-quality screenshot...")

            # Method 1: Element screenshot with validation
            try:
                element.screenshot(screenshot_path)
                if self._validate_screenshot_quality(screenshot_path):
                    print(f"   ✅ Element screenshot successful")
                    return True
                else:
                    print(f"   ⚠️ Element screenshot quality insufficient")
            except Exception as e:
                print(f"   ⚠️ Element screenshot failed: {e}")

            # Method 2: Full page screenshot with element focus
            try:
                # Scroll element to center of viewport for better capture
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'instant',
                        block: 'center',
                        inline: 'center'
                    });
                """, element)
                time.sleep(1)

                self.wd.save_screenshot(screenshot_path)
                if self._validate_screenshot_quality(screenshot_path):
                    print(f"   ✅ Full page screenshot successful")
                    return True
                else:
                    print(f"   ⚠️ Full page screenshot quality insufficient")
            except Exception as e:
                print(f"   ⚠️ Full page screenshot failed: {e}")

            return False

        except Exception as e:
            print(f"❌ Error in ultra quality screenshot: {e}")
            return False

    def _validate_screenshot_quality(self, screenshot_path):
        """Validate screenshot quality and detect common issues"""
        try:
            if not os.path.exists(screenshot_path):
                return False

            file_size = os.path.getsize(screenshot_path)
            if file_size < 10000:  # Less than 10KB is likely too small
                print(f"   ❌ Screenshot too small: {file_size} bytes")
                return False

            # Check image dimensions and content
            with Image.open(screenshot_path) as img:
                width, height = img.size
                if width < 100 or height < 100:
                    print(f"   ❌ Screenshot dimensions too small: {width}x{height}")
                    return False

                # Check if image is mostly black/white (common error)
                img_array = np.array(img.convert('L'))  # Convert to grayscale
                mean_brightness = np.mean(img_array)
                if mean_brightness < 10 or mean_brightness > 245:
                    print(f"   ❌ Screenshot appears corrupted (brightness: {mean_brightness})")
                    return False

                print(f"   ✅ Screenshot quality validated: {width}x{height}, {file_size/1024:.1f}KB")
                return True

        except Exception as e:
            print(f"   ❌ Error validating screenshot: {e}")
            return False

    def _safe_crop_screenshot(self, screenshot_path):
        """Safely crop screenshot with fallback to original if cropping fails"""
        try:
            print(f"   ✂️ Attempting safe crop...")

            # Create backup of original
            backup_path = screenshot_path + ".backup"
            import shutil
            shutil.copy2(screenshot_path, backup_path)

            # Just return the original screenshot path (no cropping)
            return True

            # Validate cropped result
            if cropped_path and self._validate_screenshot_quality(cropped_path):
                print(f"   ✅ Cropping successful")
                # Remove backup
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                return True
            else:
                print(f"   ⚠️ Cropping failed or produced poor quality, restoring original")
                # Restore original from backup
                if os.path.exists(backup_path):
                    shutil.move(backup_path, screenshot_path)
                return True

        except Exception as e:
            print(f"   ❌ Error in safe crop: {e}")
            # Try to restore backup if it exists
            backup_path = screenshot_path + ".backup"
            if os.path.exists(backup_path):
                try:
                    import shutil
                    shutil.move(backup_path, screenshot_path)
                    print(f"   ✅ Original screenshot restored")
                except:
                    pass
            return True  # Return True even if crop failed, original should still be valid

    def _hide_ui_overlays(self):
        """Hide UI overlays and elements that might interfere with screenshots"""
        try:
            # Common overlay selectors to hide
            overlay_selectors = [
                '.overlay',
                '.modal-overlay',
                '.popup-overlay',
                '.tooltip',
                '.dropdown-menu',
                'nav',
                'header',
                '.navigation',
                '.toolbar',
                '.controls',
                '.ui-controls',
                '[class*="overlay"]',
                '[class*="popup"]',
                '[class*="tooltip"]'
            ]

            hidden_elements = []
            for selector in overlay_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Store original display style
                            original_style = element.get_attribute('style')
                            # Hide element
                            self.wd.execute_script("arguments[0].style.display = 'none';", element)
                            hidden_elements.append((element, original_style))
                except:
                    continue

            # Store hidden elements for potential restoration
            self._hidden_elements = hidden_elements

        except Exception as e:
            print(f"Warning: Could not hide UI overlays: {e}")

    def _find_best_screenshot_target(self):
        """Find the best image element for screenshot"""
        try:
            # Priority-ordered selectors for finding the main image
            image_selectors = [
                # Modal/viewer images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]',
                '.viewer img[src*="macaulaylibrary.org"]',
                '.lightbox img[src*="macaulaylibrary.org"]',
                '.fullscreen img[src*="macaulaylibrary.org"]',
                '.MediaDisplay img',
                '.MediaViewer img',
                '.FullscreenImage img',
                '[data-testid="media-display"] img',

                # Large display images
                'img[class*="large"][src*="macaulaylibrary.org"]',
                'img[class*="full"][src*="macaulaylibrary.org"]',
                'img[class*="detail"][src*="macaulaylibrary.org"]',

                # General eBird images
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',

                # Fallback to any large visible image
                'img[width][height]'
            ]

            best_element = None
            best_size = 0

            for selector in image_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate element size
                            size = element.size
                            element_area = size['width'] * size['height']

                            # Prefer larger images
                            if element_area > best_size:
                                best_element = element
                                best_size = element_area

                except Exception as e:
                    continue

            if best_element:
                print(f"Found best screenshot target: {best_size} pixels area")
                return best_element
            else:
                print("No suitable screenshot target found")
                return None

        except Exception as e:
            print(f"Error finding screenshot target: {e}")
            return None

    def _take_enhanced_fallback_screenshot(self, index, output_dir):
        """Enhanced fallback screenshot with multiple strategies"""
        try:
            print(f"🔄 Taking enhanced fallback screenshot for image {index + 1}...")

            # Strategy 1: Try to find any large image on the page
            large_images = self._find_large_images_on_page()
            if large_images:
                for img in large_images[:3]:  # Try top 3 largest images
                    try:
                        screenshot_filename = f"ebird_fallback_{index + 1:03d}.png"
                        screenshot_path = os.path.join(output_dir, screenshot_filename)

                        # Scroll to image and take screenshot
                        self.wd.execute_script("arguments[0].scrollIntoView(true);", img)
                        time.sleep(1)

                        img.screenshot(screenshot_path)

                        if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 5000:
                            print(f"✅ Enhanced fallback screenshot saved: {screenshot_filename}")
                            return screenshot_path
                    except:
                        continue

            # Strategy 2: Full page screenshot with optimization
            screenshot_filename = f"ebird_fullpage_{index + 1:03d}.png"
            screenshot_path = os.path.join(output_dir, screenshot_filename)

            # Optimize page for screenshot
            self.wd.execute_script("""
                // Hide navigation and UI elements
                var elements = document.querySelectorAll('nav, header, .navigation, .toolbar');
                elements.forEach(el => el.style.display = 'none');

                // Maximize content area
                document.body.style.margin = '0';
                document.body.style.padding = '0';
            """)

            time.sleep(1)
            self.wd.save_screenshot(screenshot_path)

            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 1000:
                print(f"✅ Full page fallback screenshot saved: {screenshot_filename}")
                return screenshot_path
            else:
                print(f"❌ Failed to create any fallback screenshot")
                return None

        except Exception as e:
            print(f"❌ Error taking enhanced fallback screenshot: {e}")
            return None

    def _find_large_images_on_page(self):
        """Find all large images on the current page"""
        try:
            all_images = self.wd.find_elements(By.TAG_NAME, 'img')
            large_images = []

            for img in all_images:
                try:
                    if img.is_displayed():
                        size = img.size
                        area = size['width'] * size['height']

                        # Consider images larger than 100x100 pixels
                        if area > 10000:  # 100x100 = 10,000 pixels
                            large_images.append((img, area))
                except:
                    continue

            # Sort by area (largest first)
            large_images.sort(key=lambda x: x[1], reverse=True)

            # Return just the image elements
            return [img for img, area in large_images]

        except Exception as e:
            print(f"❌ Error finding large images: {e}")
            return []

    def _crop_screenshot_to_bird_image(self, screenshot_path):
        """Simplified screenshot processing - no cropping, just return original"""
        try:
            print(f"📸 Processing screenshot (no cropping applied)...")
            return screenshot_path

        except Exception as e:
            print(f"❌ Error processing screenshot: {e}")
            return screenshot_path







    def scrape_ebird(self, ebird_url, output_dir, max_images=50, method='click_and_view', timeout_minutes=30, max_load_more_clicks=None, crop_to_bird=True, download_method='url'):
        """Enhanced eBird scraper with robust error handling and bulk processing"""
        print("=" * 60)
        print("🦅 ENHANCED EBIRD SCRAPER STARTING")
        print("=" * 60)
        print(f"📍 URL: {ebird_url}")
        print(f"📁 Output: {output_dir}")
        print(f"🎯 Max images: {max_images}")
        print(f"⚙️ Method: {method}")
        print(f"⏱️ Timeout: {timeout_minutes} minutes")
        print(f"🔗 Download method: {download_method}")
        print("=" * 60)

        # Create output directory
        os.makedirs(output_dir, exist_ok=True)

        # Initialize statistics
        stats = {
            'successful_downloads': 0,
            'successful_screenshots': 0,
            'failed_attempts': 0,
            'load_more_clicks': 0
        }

        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        try:
            # Navigate to eBird URL
            print(f"🌐 Navigating to eBird URL...")
            self.wd.get(ebird_url)
            time.sleep(5)

            # Process images based on method
            if method == 'click_and_view':
                downloaded_count = self._process_click_and_view_method(
                    output_dir, max_images, stats, timeout_seconds, start_time, max_load_more_clicks, crop_to_bird, download_method
                )
            else:
                print(f"❌ Unknown method: {method}")
                return 0

            # Print final statistics
            self._print_final_stats(stats, start_time, output_dir)
            return stats['successful_downloads'] + stats['successful_screenshots']

        except Exception as e:
            print(f"❌ Error in eBird scraping: {e}")
            import traceback
            traceback.print_exc()
            return 0

    def _process_click_and_view_method(self, output_dir, max_images, stats, timeout_seconds, start_time, max_load_more_clicks=None, crop_to_bird=True, download_method='screenshot'):
        """Process images using streamlined chevron navigation method - Screenshot-based capture"""
        print("\n🖱️ Using STREAMLINED CHEVRON NAVIGATION method...")
        print("📋 Process: 1) Click First Image → 2) Screenshot → 3) Click Chevron → 4) Screenshot → 5) Repeat Until No More Chevrons")

        try:
            # Step 1: Find the first clickable image to start the photo viewer
            print(f"\n🔍 Finding first clickable image to start photo viewer...")
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("❌ No clickable images found")
                return 0

            print(f"✅ Found {len(clickable_images)} clickable images")
            print(f"🎯 Starting chevron navigation from first image (max {max_images} images)")

            # Step 2: Click the first image to enter photo viewer
            first_image = clickable_images[0]
            print(f"\n📸 Clicking first image to enter photo viewer...")

            if not self._click_image_to_enter_viewer(first_image):
                print("❌ Failed to enter photo viewer")
                return 0

            # Step 3: Process images using chevron navigation within photo viewer
            processed_count = self._process_images_with_chevron_navigation(output_dir, max_images, stats, timeout_seconds, start_time)

            return processed_count

        except Exception as e:
            print(f"❌ Error in streamlined chevron navigation method: {e}")
            import traceback
            traceback.print_exc()
            return 0

    def _print_final_stats(self, stats, start_time, output_dir):
        """Print final statistics"""
        try:
            total_time = time.time() - start_time
            total_processed = stats['successful_downloads'] + stats['successful_screenshots']

            print("\n" + "=" * 60)
            print("🎊 SCRAPING COMPLETED!")
            print("=" * 60)
            print(f"⏱️ Total time: {total_time:.1f} seconds")
            print(f"📁 Output directory: {output_dir}")
            print(f"✅ Successful downloads: {stats['successful_downloads']}")
            print(f"📸 Successful screenshots: {stats['successful_screenshots']}")
            print(f"❌ Failed attempts: {stats['failed_attempts']}")
            print(f"📊 Total processed: {total_processed}")
            print("=" * 60)

        except Exception as e:
            print(f"Error printing final stats: {e}")

    def _find_full_resolution_image_fast(self):
        """Find full resolution image URL quickly"""
        try:
            # Look for high resolution image URLs
            selectors = [
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',
                'img[data-src*="macaulaylibrary.org"]',
                'img[data-src*="cdn.download.ams.birds.cornell.edu"]'
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for img in images:
                        src = img.get_attribute('src') or img.get_attribute('data-src')
                        if src and ('macaulaylibrary.org' in src or 'cdn.download.ams.birds.cornell.edu' in src):
                            # Look for high resolution indicators
                            if any(size in src for size in ['1200', '1920', '2048', 'original', 'full']):
                                return src
                            # If no size indicator, return the URL anyway
                            return src
                except:
                    continue

            return None

        except Exception as e:
            print(f"Error finding image URL: {e}")
            return None

    def _download_image(self, url, filename, output_dir):
        """Download image from URL"""
        try:
            import requests

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }

            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()

            filepath = os.path.join(output_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)

            return os.path.exists(filepath)

        except Exception as e:
            print(f"Error downloading image: {e}")
            return False

    def _return_to_main_page_with_context(self, index):
        """Return to main gallery page"""
        try:
            # Try to close modal/viewer by pressing ESC
            from selenium.webdriver.common.keys import Keys
            self.wd.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
            time.sleep(2)

            # Or try to find and click close button
            close_selectors = [
                'button[aria-label*="close"]',
                'button[aria-label*="Close"]',
                '.close-button',
                '[data-testid="close"]',
                'button:contains("×")'
            ]

            for selector in close_selectors:
                try:
                    if ':contains(' in selector:
                        # Handle contains selector
                        buttons = self.wd.find_elements(By.TAG_NAME, 'button')
                        for btn in buttons:
                            if '×' in btn.text:
                                btn.click()
                                time.sleep(1)
                                return True
                    else:
                        close_btn = self.wd.find_element(By.CSS_SELECTOR, selector)
                        if close_btn.is_displayed():
                            close_btn.click()
                            time.sleep(1)
                            return True
                except:
                    continue

            return True

        except Exception as e:
            print(f"Error returning to main page: {e}")
            return False



def main():
    """Main function to run the eBird scraper"""
    parser = argparse.ArgumentParser(description='Enhanced eBird Image Scraper')

    # Mode selection
    parser.add_argument('--mode', choices=['ebird'], required=True,
                        help='Scraper mode')

    # eBird specific arguments
    parser.add_argument('--ebird_url', type=str,
                        help='eBird media catalog URL')

    parser.add_argument('--max_images', type=int, default=50,
                        help='Maximum number of images to download')

    parser.add_argument('--method', choices=['click_and_view'], default='click_and_view',
                        help='Scraping method')

    parser.add_argument('--timeout', type=int, default=30,
                        help='Timeout in minutes')

    parser.add_argument('--max_load_more_clicks', type=int, default=5,
                        help='Maximum number of Load More button clicks')

    parser.add_argument('--out_directory', type=str, default='./ebird_images',
                        help='Output directory for downloaded images')

    parser.add_argument('--download_method', choices=['url', 'screenshot'], default='url',
                        help='Download method: url (direct download) or screenshot')

    parser.add_argument('--ultra_quality', action='store_true',
                        help='Enable ultra quality mode for better image quality')

    # Cropping arguments (disabled)
    parser.add_argument('--enable_cropping', action='store_true',
                        help='Enable simplified bird image cropping (DISABLED)')

    parser.add_argument('--enable_advanced_cropping', action='store_true',
                        help='Enable advanced saliency-based intelligent cropping (DISABLED)')

    parser.add_argument('--disable_cropping', action='store_true',
                        help='Disable all cropping - keep full screenshots')

    args = parser.parse_args()

    if args.mode == 'ebird':
        if not args.ebird_url:
            print("❌ Error: --ebird_url is required for eBird mode")
            return

        # Initialize scraper
        ebird_scraper = EBirdScraperBot(headless=False)

        # All cropping is disabled
        crop_to_bird = False
        print("🔧 All cropping DISABLED - keeping original images")

        ebird_scraper.scrape_ebird(
            ebird_url=args.ebird_url,
            output_dir=args.out_directory,
            max_images=args.max_images,
            method=args.method,
            timeout_minutes=args.timeout,
            max_load_more_clicks=args.max_load_more_clicks,
            crop_to_bird=crop_to_bird,
            download_method=args.download_method
        )

if __name__ == "__main__":
    main()
