#!/usr/bin/env python3
"""
Test script untuk chevron navigation yang sudah diperbaiki
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def test_fixed_chevron_navigation():
    """Test chevron navigation dengan perbaikan RIGHT arrow dan image change validation"""
    print("🔧 Testing FIXED Chevron Navigation")
    print("=" * 50)
    
    # Test URL
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo"
    output_dir = "test_fixed_chevron"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Initialize scraper
        print("🚀 Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=False)
        
        # Navigate to URL
        print("🌐 Navigating to eBird URL...")
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Find and click first image
        print("🔍 Finding first clickable image...")
        clickable_images = scraper._get_clickable_images_from_page()
        
        if not clickable_images:
            print("❌ No clickable images found")
            return False
            
        print(f"✅ Found {len(clickable_images)} clickable images")
        
        # Enter photo viewer
        print("📸 Entering photo viewer...")
        if not scraper._click_image_to_enter_viewer(clickable_images[0]):
            print("❌ Failed to enter photo viewer")
            return False
            
        print("✅ Successfully entered photo viewer")
        
        # Test multiple images with detailed validation
        image_sources = []  # Track image sources to verify they're different
        
        for i in range(5):  # Test 5 images
            print(f"\n{'='*40}")
            print(f"🖼️ PROCESSING IMAGE {i+1}/5")
            print(f"{'='*40}")
            
            # Get current image source
            current_src = scraper._get_current_image_src()
            print(f"📸 Current image source: {current_src[:60] if current_src else 'Unknown'}...")
            
            # Check if this image is different from previous ones
            if current_src:
                if current_src in image_sources:
                    print(f"⚠️ WARNING: This image source was already seen before!")
                    print(f"   Previous images: {len(image_sources)}")
                    for j, prev_src in enumerate(image_sources):
                        print(f"   {j+1}. {prev_src[:60]}...")
                else:
                    print(f"✅ New unique image detected")
                    image_sources.append(current_src)
            
            # Take screenshot
            print(f"📸 Taking screenshot...")
            screenshot_path = scraper._take_photo_viewer_screenshot(i, output_dir)
            
            if screenshot_path:
                file_size = os.path.getsize(screenshot_path)
                print(f"✅ Screenshot saved: {os.path.basename(screenshot_path)} ({file_size/1024:.1f} KB)")
            else:
                print(f"❌ Screenshot failed")
            
            # If not the last image, navigate to next
            if i < 4:
                print(f"\n➡️ Navigating to next image...")
                
                # Try to click RIGHT chevron
                if scraper._click_chevron_next():
                    print(f"✅ Successfully navigated to next image")
                else:
                    print(f"❌ Failed to navigate to next image")
                    break
            else:
                print(f"\n🏁 Completed processing all 5 images")
        
        # Final validation
        print(f"\n📊 FINAL VALIDATION:")
        print(f"   Total unique images processed: {len(image_sources)}")
        print(f"   Expected: 5 images")
        
        if len(image_sources) >= 4:  # Allow for some tolerance
            print(f"✅ SUCCESS: Found {len(image_sources)} unique images")
            success = True
        else:
            print(f"❌ FAILED: Only found {len(image_sources)} unique images")
            success = False
        
        # Check screenshot files
        files = [f for f in os.listdir(output_dir) if f.endswith('.png')]
        print(f"   Screenshot files created: {len(files)}")
        
        for file in files:
            file_path = os.path.join(output_dir, file)
            file_size = os.path.getsize(file_path)
            print(f"     - {file} ({file_size/1024:.1f} KB)")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            scraper.wd.quit()
        except:
            pass

def compare_screenshots():
    """Compare screenshots to verify they're different"""
    print(f"\n🔍 COMPARING SCREENSHOTS...")
    
    output_dir = "test_fixed_chevron"
    
    if not os.path.exists(output_dir):
        print("❌ No output directory found")
        return False
    
    files = sorted([f for f in os.listdir(output_dir) if f.endswith('.png')])
    
    if len(files) < 2:
        print("❌ Need at least 2 screenshots to compare")
        return False
    
    try:
        from PIL import Image
        import hashlib
        
        hashes = []
        
        for file in files:
            file_path = os.path.join(output_dir, file)
            
            # Calculate file hash
            with open(file_path, 'rb') as f:
                file_hash = hashlib.md5(f.read()).hexdigest()
            
            # Also try to open as image and get basic info
            try:
                img = Image.open(file_path)
                size = img.size
                print(f"   {file}: {size[0]}x{size[1]}, hash: {file_hash[:8]}...")
            except:
                print(f"   {file}: hash: {file_hash[:8]}...")
            
            hashes.append(file_hash)
        
        # Check for duplicates
        unique_hashes = set(hashes)
        
        print(f"\n📊 Comparison Results:")
        print(f"   Total files: {len(files)}")
        print(f"   Unique hashes: {len(unique_hashes)}")
        
        if len(unique_hashes) == len(files):
            print(f"✅ All screenshots are DIFFERENT - Navigation working correctly!")
            return True
        else:
            print(f"❌ Found DUPLICATE screenshots - Navigation not working properly!")
            
            # Show which files are duplicates
            hash_to_files = {}
            for i, hash_val in enumerate(hashes):
                if hash_val not in hash_to_files:
                    hash_to_files[hash_val] = []
                hash_to_files[hash_val].append(files[i])
            
            for hash_val, file_list in hash_to_files.items():
                if len(file_list) > 1:
                    print(f"   Duplicate hash {hash_val[:8]}: {', '.join(file_list)}")
            
            return False
    
    except ImportError:
        print("⚠️ PIL not available, using file size comparison...")
        
        sizes = []
        for file in files:
            file_path = os.path.join(output_dir, file)
            size = os.path.getsize(file_path)
            sizes.append(size)
            print(f"   {file}: {size} bytes")
        
        unique_sizes = set(sizes)
        
        if len(unique_sizes) > 1:
            print(f"✅ Screenshots have different sizes - likely different images")
            return True
        else:
            print(f"❌ All screenshots have same size - might be duplicates")
            return False

def main():
    """Main test function"""
    print("🔧 FIXED CHEVRON NAVIGATION TEST")
    print("=" * 50)
    
    # Test 1: Navigation test
    navigation_success = test_fixed_chevron_navigation()
    
    # Test 2: Screenshot comparison
    comparison_success = compare_screenshots()
    
    # Final result
    print(f"\n{'='*50}")
    print("📊 FINAL RESULTS")
    print(f"{'='*50}")
    
    print(f"Navigation Test: {'✅ PASS' if navigation_success else '❌ FAIL'}")
    print(f"Screenshot Comparison: {'✅ PASS' if comparison_success else '❌ FAIL'}")
    
    overall_success = navigation_success and comparison_success
    
    if overall_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"✅ RIGHT chevron navigation is working correctly")
        print(f"✅ Screenshots show different images")
    else:
        print(f"\n⚠️ SOME TESTS FAILED!")
        if not navigation_success:
            print(f"❌ Navigation test failed - check chevron button detection")
        if not comparison_success:
            print(f"❌ Screenshot comparison failed - images might be duplicates")
    
    return 0 if overall_success else 1

if __name__ == "__main__":
    sys.exit(main())
