#!/usr/bin/env python3
"""
Test script khusus untuk direct download foto eBird
"""

import os
import sys
import time
from scraperBot import EBirdScraperBot

def test_photo_direct_download():
    """Test direct download dengan URL foto yang benar"""
    print("🧪 Testing Photo Direct Download")
    print("=" * 50)
    
    # Test URL untuk foto (bukan audio)
    test_url = "https://ebird.org/media/catalog?taxonCode=javmun1&mediaType=photo&sort=rating_rank_desc"
    output_dir = "test_photo_direct"
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # Initialize scraper
        print("🚀 Initializing eBird scraper...")
        scraper = EBirdScraperBot(headless=False)
        
        # Navigate to URL
        print("🌐 Navigating to eBird photo URL...")
        scraper.wd.get(test_url)
        time.sleep(5)
        
        # Find clickable images
        print("🔍 Finding clickable images...")
        clickable_images = scraper._get_clickable_images_from_page()
        
        if not clickable_images:
            print("❌ No clickable images found")
            return False
            
        print(f"✅ Found {len(clickable_images)} clickable images")
        
        # Test direct download on first few images
        success_count = 0
        
        for i in range(min(3, len(clickable_images))):
            print(f"\n📸 Testing image {i+1}...")
            
            # Click image to enter viewer
            clickable_img = clickable_images[i]
            if scraper._click_image_to_enter_viewer(clickable_img):
                print("✅ Entered photo viewer")
                
                # Get current URL and page source for analysis
                current_url = scraper.wd.current_url
                page_source = scraper.wd.page_source
                
                print(f"🔗 Current URL: {current_url[:80]}...")
                
                # Extract asset ID
                asset_id = scraper.direct_downloader.extract_asset_id(current_url)
                if not asset_id:
                    asset_id = scraper._extract_asset_id_from_page_elements()
                
                print(f"🆔 Asset ID: {asset_id}")
                
                # Detect media type
                media_type = scraper.direct_downloader.detect_media_type(page_source)
                print(f"📁 Detected media type: {media_type}")
                
                # Force photo type for this test
                media_type = 'photo'
                print(f"🔧 Forcing media type to: {media_type}")
                
                # Try direct download
                result = scraper.direct_downloader.download_media(
                    asset_id=asset_id,
                    media_type=media_type,
                    output_dir=output_dir,
                    index=i
                )
                
                if result:
                    print(f"✅ Direct download successful: {os.path.basename(result)}")
                    success_count += 1
                else:
                    print(f"❌ Direct download failed")
                
                # Close viewer and return to gallery
                try:
                    scraper.wd.execute_script("document.querySelector('[aria-label*=\"close\"], .close, .modal-close')?.click();")
                    time.sleep(2)
                except:
                    pass
                
            else:
                print("❌ Failed to enter photo viewer")
            
            time.sleep(2)  # Brief pause between tests
        
        print(f"\n📊 Results: {success_count}/3 direct downloads successful")
        
        # Check output files
        if os.path.exists(output_dir):
            files = [f for f in os.listdir(output_dir) if f.endswith('.jpg')]
            print(f"📁 JPG files created: {len(files)}")
            
            for file in files:
                file_path = os.path.join(output_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"   - {file} ({file_size/1024:.1f} KB)")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        try:
            scraper.wd.quit()
        except:
            pass

def test_different_bird_species():
    """Test direct download dengan spesies burung yang berbeda"""
    print("\n🧪 Testing Different Bird Species")
    print("=" * 50)
    
    # Test URLs untuk spesies yang berbeda
    test_species = [
        {"name": "Java Sparrow", "code": "javmun1"},
        {"name": "White-rumped Shama", "code": "whirsh1"},
        {"name": "Oriental Magpie-Robin", "code": "orimar1"}
    ]
    
    results = {}
    
    for species in test_species:
        print(f"\n🦅 Testing {species['name']}...")
        
        test_url = f"https://ebird.org/media/catalog?taxonCode={species['code']}&mediaType=photo&sort=rating_rank_desc"
        output_dir = f"test_{species['code']}"
        os.makedirs(output_dir, exist_ok=True)
        
        try:
            scraper = EBirdScraperBot(headless=False)
            scraper.wd.get(test_url)
            time.sleep(5)
            
            # Get first image
            clickable_images = scraper._get_clickable_images_from_page()
            
            if clickable_images and scraper._click_image_to_enter_viewer(clickable_images[0]):
                # Extract asset ID and try direct download
                current_url = scraper.wd.current_url
                asset_id = scraper.direct_downloader.extract_asset_id(current_url)
                
                if not asset_id:
                    asset_id = scraper._extract_asset_id_from_page_elements()
                
                if asset_id:
                    result = scraper.direct_downloader.download_media(
                        asset_id=asset_id,
                        media_type='photo',
                        output_dir=output_dir,
                        index=0
                    )
                    
                    results[species['name']] = {
                        'success': result is not None,
                        'asset_id': asset_id,
                        'file': result
                    }
                else:
                    results[species['name']] = {'success': False, 'error': 'No asset ID'}
            else:
                results[species['name']] = {'success': False, 'error': 'No images or viewer failed'}
            
            scraper.wd.quit()
            
        except Exception as e:
            results[species['name']] = {'success': False, 'error': str(e)}
    
    # Print results
    print(f"\n📊 Species Test Results:")
    successful = 0
    for species_name, result in results.items():
        if result['success']:
            print(f"   ✅ {species_name}: Asset ID {result.get('asset_id')}")
            successful += 1
        else:
            print(f"   ❌ {species_name}: {result.get('error', 'Failed')}")
    
    print(f"\n📈 Success rate: {successful}/{len(test_species)} species")
    return successful > 0

def main():
    """Main test function"""
    print("🧪 PHOTO DIRECT DOWNLOAD TESTING")
    print("=" * 60)
    
    tests = [
        ("Photo Direct Download", test_photo_direct_download),
        ("Different Bird Species", test_different_bird_species)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"🧪 Running: {test_name}")
        print(f"{'='*60}")
        
        try:
            success = test_func()
            results[test_name] = success
            print(f"✅ {test_name}: {'PASSED' if success else 'FAILED'}")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results[test_name] = False
    
    # Final summary
    print(f"\n{'='*60}")
    print("📊 FINAL TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_name, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status} {test_name}")
    
    print(f"\n📈 Overall Results: {passed}/{total} tests passed")
    
    if passed == total:
        print(f"🎉 ALL TESTS PASSED! Photo direct download is working!")
    else:
        print(f"⚠️ Some tests failed. Check the logs above for details.")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
